<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车型管理 - 管理后台</title>
    
    <!-- 样式文件 -->
    <link href="../css/custom.css" rel="stylesheet" type="text/css">
    
    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <style>
        body {
            margin: 0;
            background: #f5f5f5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            flex-shrink: 0;
        }
        
        .sidebar-header {
            padding: 20px;
            background: #be131b;
            text-align: center;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            border-bottom: 1px solid #34495e;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .top-bar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }
        
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .btn-primary {
            background: #be131b;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .car-models-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .car-model-row:hover {
            background: #f8f9fa;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #be131b;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 32px;
            font-weight: bold;
            color: #be131b;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>管理后台</h2>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="products.html">📦 产品管理</a></li>
                <li><a href="users.html">👥 用户管理</a></li>
                <li><a href="car-models.html" class="active">🚗 车型管理</a></li>
                <li><a href="customer-service.html">💬 客服管理</a></li>
                <li><a href="#" onclick="logout()" style="color: #ff6b6b;">🚪 退出</a></li>
            </ul>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="top-bar">
                <h1 class="page-title">车型管理</h1>
                <div class="user-info">
                    <span>欢迎，<span id="admin-username">管理员</span></span>
                    <button class="btn btn-danger" onclick="logout()">退出登录</button>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stats-card">
                        <div class="stats-number" id="total-car-models">0</div>
                        <div class="stats-label">总车型数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="total-brands">0</div>
                        <div class="stats-label">品牌数量</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-number" id="linked-models">0</div>
                        <div class="stats-label">已关联产品</div>
                    </div>
                </div>
                
                <!-- 工具栏 -->
                <div class="toolbar">
                    <div>
                        <input type="text" id="search-input" placeholder="搜索车型..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
                        <button class="btn btn-secondary" onclick="searchCarModels()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetSearch()">重置</button>
                    </div>
                    
                    <div>
                        <button class="btn btn-primary" onclick="showAddCarModelModal()">➕ 添加车型</button>
                        <button class="btn btn-secondary" onclick="exportCarModels()">📤 导出车型</button>
                    </div>
                </div>
                
                <!-- 车型表格 -->
                <div class="car-models-table">
                    <div class="table-header">
                        <h3>车型列表</h3>
                        <div>
                            <span id="car-models-count">0</span> 个车型
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>品牌</th>
                                    <th>车型名称</th>
                                    <th>具体型号</th>
                                    <th>年份范围</th>
                                    <th>发动机</th>
                                    <th>变速箱</th>
                                    <th>关联产品</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="car-models-tbody">
                                <tr>
                                    <td colspan="9" class="loading">
                                        <div class="spinner"></div>
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/supabase-config.js"></script>
    <script src="js/admin-common.js"></script>
    <script>
        let allCarModels = [];
        let filteredCarModels = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAdminAuth()) {
                loadCarModels();
            }
        });

        // 加载车型数据
        async function loadCarModels() {
            try {
                console.log('开始加载车型数据...');

                const { data: carModels, error } = await supabase
                    .from('car_models')
                    .select('*')
                    .order('brand', { ascending: true });

                if (error) {
                    console.error('加载车型数据失败:', error);
                    showError('加载车型数据失败: ' + error.message);
                    return;
                }

                allCarModels = carModels || [];
                filteredCarModels = [...allCarModels];
                
                displayCarModels();
                updateStats();

            } catch (error) {
                console.error('加载车型失败:', error);
                showError('加载车型数据失败');
            }
        }

        // 显示车型列表
        function displayCarModels() {
            const tbody = document.getElementById('car-models-tbody');
            
            if (filteredCarModels.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px; color: #666;">暂无车型数据</td></tr>';
                return;
            }

            let html = '';
            filteredCarModels.forEach(carModel => {
                html += `
                    <tr class="car-model-row">
                        <td>${carModel.brand || ''}</td>
                        <td>${carModel.brand_name || ''}</td>
                        <td>${carModel.model_name || ''}</td>
                        <td>${carModel.year_range || ''}</td>
                        <td>${carModel.engine_type || ''}</td>
                        <td>${carModel.transmission || ''}</td>
                        <td>${carModel.product_id || '未关联'}</td>
                        <td>
                            <span style="color: ${carModel.is_active ? '#28a745' : '#dc3545'};">
                                ${carModel.is_active ? '启用' : '禁用'}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-secondary" onclick="editCarModel('${carModel.id}')" style="margin-right: 5px;">编辑</button>
                            <button class="btn btn-danger" onclick="deleteCarModel('${carModel.id}')">删除</button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
            document.getElementById('car-models-count').textContent = filteredCarModels.length;
        }

        // 更新统计信息
        function updateStats() {
            const totalCarModels = allCarModels.length;
            const totalBrands = new Set(allCarModels.map(model => model.brand)).size;
            const linkedModels = allCarModels.filter(model => model.product_id).length;

            document.getElementById('total-car-models').textContent = totalCarModels;
            document.getElementById('total-brands').textContent = totalBrands;
            document.getElementById('linked-models').textContent = linkedModels;
        }

        // 搜索车型
        function searchCarModels() {
            const searchTerm = document.getElementById('search-input').value.trim().toLowerCase();
            
            if (!searchTerm) {
                filteredCarModels = [...allCarModels];
            } else {
                filteredCarModels = allCarModels.filter(carModel => 
                    (carModel.brand && carModel.brand.toLowerCase().includes(searchTerm)) ||
                    (carModel.brand_name && carModel.brand_name.toLowerCase().includes(searchTerm)) ||
                    (carModel.model_name && carModel.model_name.toLowerCase().includes(searchTerm)) ||
                    (carModel.product_id && carModel.product_id.toLowerCase().includes(searchTerm))
                );
            }
            
            displayCarModels();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('search-input').value = '';
            filteredCarModels = [...allCarModels];
            displayCarModels();
        }

        // 显示添加车型模态框
        function showAddCarModelModal() {
            alert('添加车型功能正在开发中...');
        }

        // 编辑车型
        function editCarModel(carModelId) {
            alert('编辑车型功能正在开发中...');
        }

        // 删除车型
        async function deleteCarModel(carModelId) {
            if (!confirm('确定要删除这个车型吗？此操作不可恢复。')) {
                return;
            }

            try {
                const { error } = await supabase
                    .from('car_models')
                    .delete()
                    .eq('id', carModelId);

                if (error) throw error;

                showSuccess('车型删除成功');
                loadCarModels();

            } catch (error) {
                console.error('删除车型失败:', error);
                showError('删除车型失败: ' + error.message);
            }
        }

        // 导出车型
        function exportCarModels() {
            alert('导出车型功能正在开发中...');
        }
    </script>
</body>
</html>
