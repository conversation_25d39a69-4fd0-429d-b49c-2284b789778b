# 春晟机械网站手动部署指南

## 目标服务器信息
- **服务器IP**: **************
- **用户名**: root
- **密码**: Mm.124578

## 方法一：使用Git Bash运行自动脚本（推荐）

### 1. 打开Git Bash
在项目目录右键选择 "Git Bash Here"

### 2. 运行部署脚本
```bash
./quick-deploy.sh
```

如果提示sshpass未安装，请继续下面的手动步骤。

## 方法二：手动部署步骤

### 1. 连接到服务器
使用SSH客户端（如PuTTY）连接到服务器：
- 主机：**************
- 用户名：root
- 密码：Mm.124578

### 2. 在服务器上安装Nginx
```bash
# 对于CentOS/RHEL系统
dnf install -y nginx
# 或者
yum install -y nginx

# 对于Ubuntu/Debian系统
apt-get update
apt-get install -y nginx

# 启动Nginx
systemctl start nginx
systemctl enable nginx
```

### 3. 创建网站目录
```bash
mkdir -p /var/www/chunsheng
```

### 4. 上传网站文件
使用以下任一方法上传文件：

#### 方法A：使用SCP（推荐）
在本地Git Bash中运行：
```bash
scp -r ./* root@**************:/var/www/chunsheng/
```

#### 方法B：使用WinSCP
1. 下载并安装WinSCP
2. 连接到服务器（IP: **************, 用户: root, 密码: Mm.124578）
3. 将本地项目文件上传到 `/var/www/chunsheng/` 目录

#### 方法C：使用FileZilla
1. 下载并安装FileZilla
2. 使用SFTP协议连接到服务器
3. 上传所有项目文件到 `/var/www/chunsheng/`

### 5. 配置Nginx
在服务器上创建Nginx配置文件：
```bash
cat > /etc/nginx/conf.d/chunsheng.conf << 'EOF'
server {
    listen 80;
    server_name **************;
    root /var/www/chunsheng;
    index index.html index.htm;

    access_log /var/log/nginx/chunsheng_access.log;
    error_log /var/log/nginx/chunsheng_error.log;

    location / {
        try_files $uri $uri/ =404;
    }

    location /admin/ {
        try_files $uri $uri/ =404;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    client_max_body_size 20M;
}
EOF
```

### 6. 设置文件权限
```bash
chown -R nginx:nginx /var/www/chunsheng
chmod -R 755 /var/www/chunsheng
find /var/www/chunsheng -type f -exec chmod 644 {} \;
```

### 7. 测试并重启Nginx
```bash
nginx -t
systemctl restart nginx
```

### 8. 配置防火墙
```bash
# 对于CentOS/RHEL (firewalld)
systemctl start firewalld
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --reload

# 对于Ubuntu/Debian (ufw)
ufw enable
ufw allow 80
ufw allow 443
```

### 9. 验证部署
```bash
# 检查文件
ls -la /var/www/chunsheng/

# 检查Nginx状态
systemctl status nginx

# 检查端口监听
ss -tlnp | grep :80
```

## 访问网站

部署完成后，可以通过以下地址访问：

- **主页**: http://**************
- **产品页面**: http://**************/products.html
- **管理后台**: http://**************/admin/login.html
- **用户登录**: http://**************/login.html

## 管理员登录信息

- **用户名**: admin
- **密码**: admin123

## 故障排除

### 1. 无法访问网站
```bash
# 检查Nginx状态
systemctl status nginx

# 检查防火墙
firewall-cmd --list-all

# 检查端口
ss -tlnp | grep :80
```

### 2. 403 Forbidden错误
```bash
# 检查文件权限
ls -la /var/www/chunsheng/

# 重新设置权限
chown -R nginx:nginx /var/www/chunsheng
chmod -R 755 /var/www/chunsheng
```

### 3. 502 Bad Gateway错误
```bash
# 检查Nginx配置
nginx -t

# 重启Nginx
systemctl restart nginx
```

## 常用维护命令

```bash
# 查看访问日志
tail -f /var/log/nginx/chunsheng_access.log

# 查看错误日志
tail -f /var/log/nginx/chunsheng_error.log

# 重启Nginx
systemctl restart nginx

# 重新加载Nginx配置
systemctl reload nginx

# 查看网站文件
ls -la /var/www/chunsheng/
```

## 注意事项

1. 确保云服务商的安全组已开放80和443端口
2. 如果使用域名，需要将域名解析到服务器IP
3. 定期备份网站文件和数据库
4. 建议配置SSL证书以支持HTTPS访问

## 联系支持

如果遇到问题，请提供：
1. 错误信息截图
2. 服务器日志：`tail -20 /var/log/nginx/error.log`
3. 系统信息：`cat /etc/os-release`
