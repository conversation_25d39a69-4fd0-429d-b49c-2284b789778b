<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .user-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .user-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 10px;
        }
        .user-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .admin { background: #dc3545; color: white; }
        .privileged { background: #fd7e14; color: white; }
        .premium { background: #198754; color: white; }
        .guest { background: #6c757d; color: white; }
        
        button {
            background: #be131b;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #a00e15;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        .current-user {
            background: #e7f3ff;
            border: 2px solid #0066cc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .admin-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .password-change {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 10px;
            align-items: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户登录测试</h1>
        
        <div id="current-user" class="current-user" style="display: none;">
            <h3>当前登录用户</h3>
            <div id="current-user-info"></div>
            <button onclick="logout()">退出登录</button>
        </div>

        <h2>所有用户列表</h2>
        <div id="users-list">
            <p>正在加载用户列表...</p>
        </div>

        <div id="admin-panel" class="admin-panel" style="display: none;">
            <h3>🔧 管理员功能 - 密码管理</h3>
            <p>作为管理员，您可以重置其他用户的密码：</p>
            <div id="password-management"></div>
        </div>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>

    <script>
        let allUsers = [];

        // 预定义的用户列表（从数据库查询结果）
        const usersList = [
            { username: "lyp4611", email: "<EMAIL>", user_type: "privileged", company_name: "", phone: "15155584110" },
            { username: "tex", email: "<EMAIL>", user_type: "privileged", company_name: "", phone: "" },
            { username: "j8", email: "<EMAIL>", user_type: "premium", company_name: "1", phone: "13373481989" },
            { username: "mengbu", email: "<EMAIL>", user_type: "premium", company_name: "1", phone: "13373481984" },
            { username: "lyp", email: "<EMAIL>", user_type: "premium", company_name: "", phone: "" },
            { username: "测试用户", email: "<EMAIL>", user_type: "premium", company_name: "测试公司", phone: "13800138000" },
            { username: "liangbu", email: "<EMAIL>", user_type: "admin", company_name: "1", phone: "13373481984" },
            { username: "admin", email: "<EMAIL>", user_type: "admin", company_name: null, phone: null }
        ];

        // 等待认证系统初始化
        window.addEventListener('authReady', function() {
            displayUsers();
            updateCurrentUser();
        });

        // 显示用户列表
        function displayUsers() {
            const container = document.getElementById('users-list');
            
            let html = '';
            usersList.forEach(user => {
                const password = user.email === '<EMAIL>' ? 'test123' : '123456';
                
                html += `
                    <div class="user-card">
                        <div class="user-info">
                            <div><strong>${user.username}</strong></div>
                            <div>${user.email}</div>
                            <div><span class="user-type ${user.user_type}">${getUserTypeDisplay(user.user_type)}</span></div>
                            <div>${user.company_name || '未填写'}</div>
                        </div>
                        <div>
                            <strong>测试密码:</strong> ${password}
                            <button onclick="testLogin('${user.email}', '${password}')" id="btn-${user.email.replace('@', '-').replace('.', '-')}">
                                测试登录
                            </button>
                            <span id="status-${user.email.replace('@', '-').replace('.', '-')}"></span>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 测试登录
        async function testLogin(email, password) {
            const btnId = 'btn-' + email.replace('@', '-').replace('.', '-');
            const statusId = 'status-' + email.replace('@', '-').replace('.', '-');
            const btn = document.getElementById(btnId);
            const status = document.getElementById(statusId);
            
            btn.disabled = true;
            btn.textContent = '登录中...';
            status.innerHTML = '';
            
            try {
                await window.auth.signIn(email, password);
                status.innerHTML = '<span class="status success">✓ 登录成功</span>';
                btn.textContent = '已登录';
                updateCurrentUser();
                
                // 如果是管理员，显示管理面板
                if (window.auth.isAdmin()) {
                    showAdminPanel();
                }
                
            } catch (error) {
                status.innerHTML = `<span class="status error">✗ ${error.message}</span>`;
                btn.disabled = false;
                btn.textContent = '重试登录';
            }
        }

        // 更新当前用户显示
        function updateCurrentUser() {
            const container = document.getElementById('current-user');
            const info = document.getElementById('current-user-info');
            
            if (window.auth.isLoggedIn()) {
                const user = window.auth.getCurrentUser();
                container.style.display = 'block';
                info.innerHTML = `
                    <p><strong>用户名:</strong> ${user.username}</p>
                    <p><strong>邮箱:</strong> ${user.email}</p>
                    <p><strong>权限:</strong> <span class="user-type ${user.user_type}">${getUserTypeDisplay(user.user_type)}</span></p>
                    <p><strong>公司:</strong> ${user.company_name || '未填写'}</p>
                `;
                
                // 重置所有按钮状态
                usersList.forEach(u => {
                    const btnId = 'btn-' + u.email.replace('@', '-').replace('.', '-');
                    const btn = document.getElementById(btnId);
                    if (btn) {
                        btn.disabled = false;
                        btn.textContent = u.email === user.email ? '当前用户' : '测试登录';
                        if (u.email === user.email) {
                            btn.disabled = true;
                        }
                    }
                });
            } else {
                container.style.display = 'none';
                document.getElementById('admin-panel').style.display = 'none';
            }
        }

        // 显示管理员面板
        function showAdminPanel() {
            const panel = document.getElementById('admin-panel');
            const management = document.getElementById('password-management');
            
            panel.style.display = 'block';
            
            let html = '';
            usersList.forEach(user => {
                if (user.email !== window.auth.getCurrentUser().email) {
                    html += `
                        <div class="password-change">
                            <div><strong>${user.username}</strong> (${user.email})</div>
                            <input type="password" id="new-pwd-${user.email.replace('@', '-').replace('.', '-')}" placeholder="新密码" value="newpass123">
                            <button onclick="changeUserPassword('${user.email}')">重置密码</button>
                        </div>
                    `;
                }
            });
            
            management.innerHTML = html;
        }

        // 管理员重置用户密码
        async function changeUserPassword(email) {
            const inputId = 'new-pwd-' + email.replace('@', '-').replace('.', '-');
            const newPassword = document.getElementById(inputId).value;

            if (!newPassword) {
                alert('请输入新密码');
                return;
            }

            if (newPassword.length < 6) {
                alert('密码长度至少6位');
                return;
            }

            try {
                // 使用管理员重置密码功能
                await window.auth.adminResetPasswordByEmail(email, newPassword);
                alert(`✅ 成功重置用户 ${email} 的密码为: ${newPassword}`);

                // 更新对应用户的测试密码显示
                updateUserPasswordDisplay(email, newPassword);

            } catch (error) {
                alert('❌ 密码重置失败: ' + error.message);
            }
        }

        // 更新用户密码显示
        function updateUserPasswordDisplay(email, newPassword) {
            // 找到对应的用户卡片并更新密码显示
            const userCards = document.querySelectorAll('.user-card');
            userCards.forEach(card => {
                if (card.innerHTML.includes(email)) {
                    const passwordText = card.querySelector('div:last-child');
                    if (passwordText) {
                        passwordText.innerHTML = passwordText.innerHTML.replace(
                            /测试密码:<\/strong> \w+/,
                            `测试密码:</strong> ${newPassword}`
                        );
                    }
                }
            });
        }

        // 退出登录
        async function logout() {
            try {
                await window.auth.signOut();
                updateCurrentUser();
                
                // 重置所有按钮状态
                usersList.forEach(user => {
                    const btnId = 'btn-' + user.email.replace('@', '-').replace('.', '-');
                    const statusId = 'status-' + user.email.replace('@', '-').replace('.', '-');
                    const btn = document.getElementById(btnId);
                    const status = document.getElementById(statusId);
                    
                    if (btn) {
                        btn.disabled = false;
                        btn.textContent = '测试登录';
                    }
                    if (status) {
                        status.innerHTML = '';
                    }
                });
                
            } catch (error) {
                alert('退出登录失败: ' + error.message);
            }
        }

        // 获取用户类型显示名称
        function getUserTypeDisplay(userType) {
            const typeNames = {
                'guest': '游客',
                'premium': '高级用户',
                'privileged': '特许用户',
                'admin': '管理员'
            };
            return typeNames[userType] || '未知';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (window.auth && window.auth.isInitialized) {
                displayUsers();
                updateCurrentUser();
            }
        });
    </script>
</body>
</html>
