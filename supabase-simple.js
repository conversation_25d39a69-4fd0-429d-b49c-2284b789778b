// 简化的Supabase客户端实现
// 替代有问题的 supabase-js.min.js 文件

(function(window) {
    'use strict';

    // Supabase配置
    const SUPABASE_URL = 'https://snckktsqwrbfwtjlvcfr.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644';

    // 创建简化的Supabase客户端
    function createSupabaseClient(url, key) {
        return {
            // 数据库查询
            from: function(table) {
                return new SupabaseTable(table, url, key);
            },
            
            // 认证相关（简化版本，实际不使用）
            auth: {
                signUp: function() {
                    return Promise.resolve({ data: null, error: { message: '请使用简化认证系统' } });
                },
                signInWithPassword: function() {
                    return Promise.resolve({ data: null, error: { message: '请使用简化认证系统' } });
                },
                signOut: function() {
                    return Promise.resolve({ error: null });
                },
                getUser: function() {
                    return Promise.resolve({ data: { user: null } });
                },
                onAuthStateChange: function() {
                    return { data: { subscription: null } };
                }
            },

            // 实时订阅功能（简化版本）
            channel: function(channelName) {
                return new SupabaseChannel(channelName, url, key);
            }
        };
    }

    // 数据库表操作类
    class SupabaseTable {
        constructor(table, url, key) {
            this.table = table;
            this.url = url;
            this.key = key;
            this.query = {
                select: '*',
                conditions: [],
                orderBy: null,
                limit: null
            };
        }

        select(columns) {
            this.query.select = columns;
            return this;
        }

        eq(column, value) {
            this.query.conditions.push({ column, operator: 'eq', value });
            return this;
        }

        neq(column, value) {
            this.query.conditions.push({ column, operator: 'neq', value });
            return this;
        }

        gt(column, value) {
            this.query.conditions.push({ column, operator: 'gt', value });
            return this;
        }

        lt(column, value) {
            this.query.conditions.push({ column, operator: 'lt', value });
            return this;
        }

        like(column, value) {
            this.query.conditions.push({ column, operator: 'like', value });
            return this;
        }

        order(column, options = {}) {
            this.query.orderBy = {
                column: column,
                ascending: options.ascending !== false
            };
            return this;
        }

        limit(count) {
            this.query.limit = count;
            return this;
        }

        // 执行查询并返回单个结果
        async single() {
            this.query.limit = 1;
            const result = await this.execute();
            return {
                data: result.data && result.data.length > 0 ? result.data[0] : null,
                error: result.error
            };
        }

        // 执行查询
        async execute() {
            try {
                // 手动构建查询参数，避免双重编码
                const queryParams = [];

                // 添加select参数（不进行额外编码）
                queryParams.push(`select=${encodeURIComponent(this.query.select)}`);

                // 添加条件参数
                this.query.conditions.forEach(condition => {
                    queryParams.push(`${encodeURIComponent(condition.column)}=${encodeURIComponent(condition.operator)}.${encodeURIComponent(condition.value)}`);
                });

                // 添加排序参数
                if (this.query.orderBy) {
                    queryParams.push(`order=${encodeURIComponent(this.query.orderBy.column)}.${this.query.orderBy.ascending ? 'asc' : 'desc'}`);
                }

                // 添加限制参数
                if (this.query.limit) {
                    queryParams.push(`limit=${this.query.limit}`);
                }

                const queryString = queryParams.join('&');
                const response = await fetch(`${this.url}/rest/v1/${this.table}?${queryString}`, {
                    method: 'GET',
                    headers: {
                        'apikey': this.key,
                        'Authorization': `Bearer ${this.key}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return { data, error: null };

            } catch (error) {
                console.error('Supabase查询失败:', error);
                return { data: null, error };
            }
        }

        // 插入数据
        async insert(data) {
            try {
                const response = await fetch(`${this.url}/rest/v1/${this.table}`, {
                    method: 'POST',
                    headers: {
                        'apikey': this.key,
                        'Authorization': `Bearer ${this.key}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(Array.isArray(data) ? data : [data])
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                return { data: result, error: null };

            } catch (error) {
                console.error('Supabase插入失败:', error);
                return { data: null, error };
            }
        }

        // 更新数据
        async update(data) {
            try {
                // 手动构建查询参数，避免双重编码
                const queryParams = [];

                // 添加条件参数
                this.query.conditions.forEach(condition => {
                    queryParams.push(`${encodeURIComponent(condition.column)}=${encodeURIComponent(condition.operator)}.${encodeURIComponent(condition.value)}`);
                });

                const queryString = queryParams.join('&');
                const response = await fetch(`${this.url}/rest/v1/${this.table}?${queryString}`, {
                    method: 'PATCH',
                    headers: {
                        'apikey': this.key,
                        'Authorization': `Bearer ${this.key}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                return { data: result, error: null };

            } catch (error) {
                console.error('Supabase更新失败:', error);
                return { data: null, error };
            }
        }

        // 删除数据
        async delete() {
            try {
                // 手动构建查询参数，避免双重编码
                const queryParams = [];

                // 添加条件参数
                this.query.conditions.forEach(condition => {
                    queryParams.push(`${encodeURIComponent(condition.column)}=${encodeURIComponent(condition.operator)}.${encodeURIComponent(condition.value)}`);
                });

                const queryString = queryParams.join('&');
                const response = await fetch(`${this.url}/rest/v1/${this.table}?${queryString}`, {
                    method: 'DELETE',
                    headers: {
                        'apikey': this.key,
                        'Authorization': `Bearer ${this.key}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return { data: null, error: null };

            } catch (error) {
                console.error('Supabase删除失败:', error);
                return { data: null, error };
            }
        }
    }

    // 实时订阅频道类（简化版本）
    class SupabaseChannel {
        constructor(channelName, url, key) {
            this.channelName = channelName;
            this.url = url;
            this.key = key;
            this.subscriptions = [];
        }

        on(event, filter, callback) {
            console.log(`📡 [REALTIME] 模拟订阅: ${event}`, filter);

            // 简化版本：不提供真实的实时功能，只是模拟
            const subscription = {
                event: event,
                filter: filter,
                callback: callback
            };

            this.subscriptions.push(subscription);
            return this;
        }

        subscribe(callback) {
            console.log(`📡 [REALTIME] 模拟频道订阅: ${this.channelName}`);

            // 模拟订阅成功
            if (callback) {
                setTimeout(() => {
                    callback('SUBSCRIBED', null);
                }, 100);
            }

            return {
                unsubscribe: () => {
                    console.log(`📡 [REALTIME] 取消订阅: ${this.channelName}`);
                }
            };
        }
    }

    // 创建全局Supabase对象
    window.supabase = {
        createClient: createSupabaseClient
    };

    // 自动创建默认客户端
    window.supabaseClient = createSupabaseClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    console.log('✅ 简化Supabase客户端已加载');

})(window);
