<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品权限测试 - 春晟机械</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .permission-level {
            display: inline-block;
            padding: 5px 10px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .guest { background: #ecf0f1; color: #2c3e50; }
        .premium { background: #f39c12; color: white; }
        .privileged { background: #9b59b6; color: white; }
        .admin { background: #e74c3c; color: white; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>产品权限测试工具</h1>
        <p>测试产品中心页面的权限控制功能</p>
        
        <div class="test-section">
            <h3>权限级别设置</h3>
            <p>点击下面的权限级别来模拟不同用户：</p>
            <div class="permission-level guest" onclick="setUserType('guest')">👤 游客权限</div>
            <div class="permission-level premium" onclick="setUserType('premium')">⭐ 普通用户</div>
            <div class="permission-level privileged" onclick="setUserType('privileged')">💎 高级用户</div>
            <div class="permission-level admin" onclick="setUserType('admin')">👑 管理员</div>
        </div>
        
        <div class="test-section">
            <h3>当前权限状态</h3>
            <div id="permission-status">等待设置权限...</div>
        </div>
        
        <div class="test-section">
            <h3>权限测试</h3>
            <button onclick="testProductCenterAccess()" style="background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">测试产品中心访问权限</button>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>权限说明</h3>
            <div style="font-size: 14px; color: #666;">
                <p><strong>👤 游客权限</strong>：浏览基本页面，首页产品中心查询</p>
                <p><strong>⭐ 普通用户</strong>：点击图片显示产品基本信息</p>
                <p><strong>💎 高级用户</strong>：可查看或下载附件</p>
                <p><strong>🔑 特殊用户</strong>：开放产品中心页面（高级搜索）</p>
                <p><strong>👑 管理员</strong>：拥有完整系统权限</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>访问产品中心</h3>
            <a href="products.html" target="_blank" style="background: #2ecc71; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">打开产品中心页面</a>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>
    <script src="js/supabase-config.js"></script>
    
    <script>
        // 全局变量
        let currentUser = null;
        let currentUserType = 'guest';
        
        // 设置用户类型
        function setUserType(userType) {
            console.log('设置用户类型为:', userType);
            
            // 创建模拟用户
            const mockUser = {
                id: 'test-user-' + Date.now(),
                username: userType === 'admin' ? '管理员' :
                         userType === 'privileged' ? '特殊用户' :
                         userType === 'premium' ? '高级用户' : '游客',
                email: userType + '@test.com',
                user_type: userType
            };
            
            // 设置本地变量
            currentUser = userType === 'guest' ? null : mockUser;
            currentUserType = userType;
            
            // 设置全局变量
            window.currentUser = currentUser;
            window.currentUserType = currentUserType;
            
            // 如果AuthManager存在，也更新它
            if (typeof window.authManager !== 'undefined') {
                window.authManager.currentUser = currentUser;
                window.authManager.userType = userType;
            }
            
            updatePermissionStatus();
        }
        
        // 更新权限状态显示
        function updatePermissionStatus() {
            const statusDiv = document.getElementById('permission-status');
            
            const user = currentUser;
            const userType = currentUserType;
            
            let html = `
                <div class="status info">
                    <strong>当前状态:</strong><br>
                    用户: ${user ? user.username : '未登录'}<br>
                    类型: ${userType}<br>
                    <br>
                    <strong>权限检查:</strong><br>
                    产品中心访问: ${checkProductCenterPermission() ? '✅ 允许' : '❌ 拒绝'}
                </div>
            `;
            
            statusDiv.innerHTML = html;
        }
        
        // 检查产品中心权限
        function checkProductCenterPermission() {
            return currentUserType === 'privileged' || currentUserType === 'admin';
        }
        
        // 测试产品中心访问权限
        function testProductCenterAccess() {
            const resultsDiv = document.getElementById('test-results');
            const hasAccess = checkProductCenterPermission();
            
            let html = `
                <div class="status ${hasAccess ? 'success' : 'error'}">
                    <strong>测试结果:</strong><br>
                    用户类型: ${currentUserType}<br>
                    产品中心访问权限: ${hasAccess ? '✅ 允许访问' : '❌ 拒绝访问'}<br>
                    <br>
                    ${hasAccess ? 
                        '该用户可以访问产品中心的高级搜索功能。' : 
                        '该用户无法访问产品中心页面，需要特殊用户或管理员权限。'
                    }
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认设置为游客
            setUserType('guest');
        });
    </script>
</body>
</html>
