<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录统计测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #be131b;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #a00e15;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 登录访问统计测试</h1>
    
    <div class="test-section">
        <h2>测试用户登录</h2>
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="1234">
        </div>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="checkUserStats()">查看用户统计</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h2>用户访问统计</h2>
        <button onclick="checkUserStats()">刷新统计</button>
        <div id="stats-result"></div>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // 等待认证系统初始化
        function waitForAuth() {
            return new Promise((resolve) => {
                if (window.auth && window.auth.isInitialized) {
                    resolve();
                } else {
                    window.addEventListener('authReady', resolve, { once: true });
                }
            });
        }

        // 测试登录功能
        async function testLogin() {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            if (!email || !password) {
                showResult('login-result', '请输入邮箱和密码', 'error');
                return;
            }

            showResult('login-result', '正在测试登录...', 'info');

            try {
                // 等待认证系统初始化
                await waitForAuth();

                console.log('🧪 开始测试登录:', email);

                // 使用认证系统登录
                const result = await window.auth.signIn(email, password);

                console.log('🧪 登录结果:', result);

                if (result.success) {
                    showResult('login-result', `✅ 登录成功！<br>用户: ${result.user.username}<br>类型: ${result.user.user_type}`, 'success');
                    
                    // 等待一下让访问统计更新
                    setTimeout(() => {
                        checkUserStats();
                    }, 1000);
                } else {
                    showResult('login-result', `❌ 登录失败: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('🧪 登录测试失败:', error);
                showResult('login-result', `❌ 登录测试失败: ${error.message}`, 'error');
            }
        }

        // 检查用户访问统计
        async function checkUserStats() {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                showResult('stats-result', '请输入邮箱', 'error');
                return;
            }

            showResult('stats-result', '正在查询用户统计...', 'info');

            try {
                // 等待Supabase初始化
                await waitForAuth();

                console.log('🧪 查询用户统计:', email);

                // 查询用户信息
                const { data: user, error } = await window.supabaseClient
                    .from('users')
                    .select('id, username, email, user_type, last_visit_at, visit_count, created_at')
                    .eq('email', email)
                    .single();

                if (error) {
                    throw error;
                }

                if (!user) {
                    showResult('stats-result', '❌ 用户不存在', 'error');
                    return;
                }

                const statsHtml = `
                    <div class="success">✅ 用户统计信息</div>
                    <pre>${JSON.stringify({
                        用户名: user.username,
                        邮箱: user.email,
                        用户类型: user.user_type,
                        访问次数: user.visit_count,
                        最近访问: user.last_visit_at ? new Date(user.last_visit_at).toLocaleString('zh-CN') : '从未访问',
                        注册时间: new Date(user.created_at).toLocaleString('zh-CN')
                    }, null, 2)}</pre>
                `;

                showResult('stats-result', statsHtml, 'success');

            } catch (error) {
                console.error('🧪 查询统计失败:', error);
                showResult('stats-result', `❌ 查询失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动查询统计
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkUserStats();
            }, 2000);
        });
    </script>
</body>
</html>
