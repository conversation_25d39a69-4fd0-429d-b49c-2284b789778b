// 产品页面专用JavaScript

let allProducts = []; // 存储所有产品数据
let filteredProducts = []; // 存储筛选后的产品数据

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保权限系统已加载
    setTimeout(() => {
        initializeProductsPage();
    }, 2500); // 增加延迟时间，确保权限系统完全初始化
});

// 检查高级搜索权限（供外部调用）
function checkAdvancedSearchPermission() {
    console.log('开始检查产品中心访问权限...');

    // 优先检查CustomAuth认证系统
    if (typeof window.auth !== 'undefined' && window.auth.isInitialized) {
        const userType = window.auth.getUserType();
        console.log('检查CustomAuth权限:', userType);
        const hasPermission = userType === 'privileged' || userType === 'admin';
        console.log('CustomAuth权限检查结果:', hasPermission);
        return hasPermission;
    }

    // 检查AuthManager
    if (typeof window.authManager !== 'undefined') {
        const userType = window.authManager.getUserType();
        console.log('检查AuthManager权限:', userType);
        const hasPermission = userType === 'privileged' || userType === 'admin';
        console.log('AuthManager权限检查结果:', hasPermission);
        return hasPermission;
    }

    // 检查全局权限变量（但只有在不是guest时才信任）
    if (typeof window.currentUserType !== 'undefined' && window.currentUserType !== 'guest') {
        console.log('检查全局权限变量:', window.currentUserType);
        const hasPermission = window.currentUserType === 'privileged' || window.currentUserType === 'admin';
        console.log('全局权限检查结果:', hasPermission);
        return hasPermission;
    }

    // 检查传统的currentUserType变量
    if (typeof currentUserType !== 'undefined' && currentUserType !== 'guest') {
        console.log('检查传统权限变量:', currentUserType);
        const hasPermission = currentUserType === 'privileged' || currentUserType === 'admin';
        console.log('传统权限检查结果:', hasPermission);
        return hasPermission;
    }

    console.log('未找到任何权限信息，默认拒绝访问');
    // 默认拒绝访问
    return false;
}

// 初始化产品页面
async function initializeProductsPage() {
    console.log('开始初始化产品页面...');

    // 首先检查访问权限
    const hasPermission = checkAdvancedSearchPermission();
    console.log('产品页面权限检查结果:', hasPermission);

    if (!hasPermission) {
        console.log('权限不足，显示访问被拒绝消息');
        showAccessDeniedMessage();
        return; // 停止初始化
    }

    console.log('权限验证通过，继续初始化产品页面');

    // 加载所有产品
    await loadAllProductsData();

    // 初始化筛选功能
    initializeFilters();

    // 显示产品
    displayFilteredProducts();

    // 初始化智能搜索功能
    await initializeSmartProductSearch();
}

// 显示访问被拒绝的消息
function showAccessDeniedMessage() {
    const container = document.querySelector('.container');
    if (container) {
        container.innerHTML = `
            <div style="text-align: center; padding: 60px 20px; background: white; border-radius: 8px; margin: 20px auto; max-width: 600px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="font-size: 48px; margin-bottom: 20px;">🔒</div>
                <h2 style="color: #e74c3c; margin-bottom: 15px;">访问权限不足</h2>
                <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
                    抱歉，产品中心的高级搜索功能仅对<strong>特殊用户</strong>和<strong>管理员</strong>开放。
                </p>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 25px;">
                    <h4 style="color: #333; margin-bottom: 10px;">权限说明：</h4>
                    <div style="text-align: left; color: #666; font-size: 14px;">
                        <p style="margin: 8px 0;">👤 <strong>游客权限</strong>：浏览基本页面，首页产品中心查询</p>
                        <p style="margin: 8px 0;">⭐ <strong>普通用户</strong>：点击图片显示产品基本信息</p>
                        <p style="margin: 8px 0;">💎 <strong>高级用户</strong>：可查看或下载附件</p>
                        <p style="margin: 8px 0;">🔑 <strong>特殊用户</strong>：开放产品中心页面（高级搜索）</p>
                        <p style="margin: 8px 0;">👑 <strong>管理员</strong>：拥有完整系统权限</p>
                    </div>
                </div>
                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                    <a href="index.html" style="background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">返回首页</a>
                    <a href="login.html" style="background: #2ecc71; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">登录账户</a>
                    <a href="register.html" style="background: #f39c12; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">注册账户</a>
                </div>
            </div>
        `;
    }
}

// 初始化智能产品搜索
async function initializeSmartProductSearch() {
    console.log('初始化智能搜索功能...');

    try {
        // 确保智能搜索对象存在
        if (typeof SmartSearch !== 'undefined') {
            window.smartSearch = new SmartSearch();
            smartSearch.setProducts(allProducts);
            console.log('智能搜索对象已创建，产品数据已设置');
        } else {
            console.warn('SmartSearch 类未找到，跳过智能搜索初始化');
        }

        // 初始化搜索界面
        if (typeof initializeSmartSearch === 'function') {
            initializeSmartSearch();
        }

        // 绑定智能搜索事件
        const smartSearchBtn = document.getElementById('smart-search-btn');
        const smartSearchInput = document.getElementById('smart-search-input');

        if (smartSearchBtn) {
            smartSearchBtn.addEventListener('click', performSmartSearch);
        }

        if (smartSearchInput) {
            smartSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSmartSearch();
                }
            });

            // 实时搜索建议
            smartSearchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length >= 2) {
                    if (typeof showSearchHistory === 'function') {
                        showSearchHistory();
                    }
                }
            });
        }

        // 绑定筛选器事件
        const categoryFilter = document.getElementById('category-filter');
        const materialFilter = document.getElementById('material-filter');
        const resetBtn = document.getElementById('reset-filters-btn');

        if (categoryFilter) categoryFilter.addEventListener('change', performSmartSearch);
        if (materialFilter) materialFilter.addEventListener('change', performSmartSearch);
        if (resetBtn) resetBtn.addEventListener('click', resetAllFilters);

        console.log('智能搜索功能初始化完成');
    } catch (error) {
        console.error('智能搜索初始化失败:', error);
    }
}

// 执行智能搜索（如果智能搜索模块不可用，则使用基本搜索）
function performSmartSearch() {
    try {
        if (typeof smartSearch !== 'undefined' && smartSearch.search) {
            // 使用智能搜索
            const query = document.getElementById('smart-search-input')?.value.trim() || '';
            const includeApproximate = document.getElementById('include-approximate')?.checked || false;
            const tolerance = parseInt(document.getElementById('tolerance-select')?.value || '3');
            const category = document.getElementById('category-filter')?.value || '';
            const material = document.getElementById('material-filter')?.value || '';

            console.log('执行智能搜索:', query);

            const results = smartSearch.search(query, {
                tolerance: tolerance,
                includeApproximate: includeApproximate
            });

            // 合并精确和近似结果
            let combinedResults = [...results.exact];
            if (includeApproximate) {
                combinedResults = combinedResults.concat(results.approximate);
            }

            // 应用传统筛选
            if (category || material) {
                combinedResults = combinedResults.filter(product => {
                    const categoryMatch = !category || product.product_category === category;
                    const materialMatch = !material || (product.material && product.material.includes(material));
                    return categoryMatch && materialMatch;
                });
            }

            // 显示智能搜索结果
            const container = document.getElementById('products-table-body');
            if (typeof displaySmartSearchResults === 'function') {
                displaySmartSearchResults(results, container);
            } else {
                // 回退到传统显示方式
                filteredProducts = combinedResults;
                displayFilteredProducts();
            }

            // 更新统计信息
            if (typeof updateSearchStats === 'function') {
                updateSearchStats(results, query);
            }

            // 显示搜索历史
            if (typeof showSearchHistory === 'function') {
                showSearchHistory();
            }

            console.log('智能搜索结果:', combinedResults.length, '个产品');
        } else {
            console.log('智能搜索不可用，使用基本搜索');
            applyFilters();
            displayFilteredProducts();
        }
    } catch (error) {
        console.error('智能搜索出错:', error);
        console.log('回退到基本搜索');
        applyFilters();
        displayFilteredProducts();
    }
}

// 重置所有筛选器
function resetAllFilters() {
    const smartSearchInput = document.getElementById('smart-search-input');
    const categoryFilter = document.getElementById('category-filter');
    const materialFilter = document.getElementById('material-filter');

    if (smartSearchInput) smartSearchInput.value = '';
    if (categoryFilter) categoryFilter.value = '';
    if (materialFilter) materialFilter.value = '';

    filteredProducts = [...allProducts];
    displayFilteredProducts();
}

// 加载所有产品数据
async function loadAllProductsData() {
    const container = document.getElementById('products-table-body');
    if (!container) {
        console.error('产品表格容器未找到');
        return;
    }

    // 显示加载状态
    container.innerHTML = `
        <tr>
            <td colspan="14" style="padding: 40px; text-align: center; color: #666;">
                <div style="font-size: 16px;">正在加载产品数据...</div>
            </td>
        </tr>
    `;

    try {
        console.log('开始加载产品数据...');

        // 使用已有的getProducts函数
        if (typeof getProducts === 'function') {
            allProducts = await getProducts();
        } else {
            console.error('getProducts函数未找到');
            allProducts = [];
        }

        console.log('加载到的产品数据:', allProducts);

        if (!allProducts || allProducts.length === 0) {
            container.innerHTML = `
                <tr>
                    <td colspan="14" style="padding: 40px; text-align: center; color: #666;">
                        暂无产品数据
                    </td>
                </tr>
            `;
            return;
        }

        filteredProducts = [...allProducts]; // 初始化筛选结果
        console.log('产品数据加载成功，共', allProducts.length, '个产品');

        // 动态加载材质选项
        loadMaterialOptions();
    } catch (error) {
        console.error('加载产品失败:', error);
        container.innerHTML = `
            <tr>
                <td colspan="14" style="padding: 40px; text-align: center; color: #666;">
                    加载产品失败，请稍后重试
                </td>
            </tr>
        `;
    }
}

// 初始化筛选功能
function initializeFilters() {
    console.log('初始化筛选功能...');

    // 检查是否有来自首页的分类选择
    const selectedCategory = sessionStorage.getItem('selectedCategory');
    const categoryFilter = document.getElementById('category-filter');
    if (selectedCategory && categoryFilter) {
        categoryFilter.value = selectedCategory;
        console.log('从首页接收到分类筛选:', selectedCategory);
        sessionStorage.removeItem('selectedCategory');
        setTimeout(() => applyFilters(), 100);
    }

    // 绑定所有筛选器的事件
    const filterIds = [
        'product-code-filter',
        'product-name-filter',
        'category-filter',
        'specifications-filter',
        'material-filter',
        'thickness-filter',
        'notes-filter',
        'shape-code-filter',
        'process1-filter',
        'process2-filter',
        'process3-filter',
        'process4-filter',
        'car-brand-filter'
    ];

    // 为所有筛选器绑定实时搜索事件
    filterIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            if (element.tagName === 'SELECT') {
                // 下拉框使用change事件
                element.addEventListener('change', applyFilters);
            } else {
                // 输入框使用input事件（延迟搜索）
                element.addEventListener('input', function() {
                    clearTimeout(window.searchTimeout);
                    window.searchTimeout = setTimeout(() => {
                        applyFilters();
                    }, 500);
                });

                // 输入框也绑定回车事件
                element.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        applyFilters();
                    }
                });
            }
        }
    });

    // 智能搜索框事件
    const smartSearchInput = document.getElementById('smart-search-input');
    if (smartSearchInput) {
        smartSearchInput.addEventListener('input', function() {
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(() => {
                applyFilters();
            }, 500);
        });

        smartSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    }

    // 搜索按钮事件
    const searchBtn = document.getElementById('smart-search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', applyFilters);
    }

    // 查询按钮事件
    const searchFiltersBtn = document.getElementById('search-filters-btn');
    if (searchFiltersBtn) {
        searchFiltersBtn.addEventListener('click', applyFilters);
    }

    // 重置按钮事件
    const resetBtn = document.getElementById('reset-filters-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetFilters);
    }

    // 导出按钮事件
    const exportBtn = document.getElementById('export-results-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportResults);
    }

    console.log('筛选功能初始化完成');
}

// 应用筛选
function applyFilters() {
    // 获取所有筛选条件
    const filters = {
        productCode: document.getElementById('product-code-filter')?.value.trim().toLowerCase() || '',
        productName: document.getElementById('product-name-filter')?.value.trim().toLowerCase() || '',
        category: document.getElementById('category-filter')?.value || '',
        specifications: document.getElementById('specifications-filter')?.value.trim().toLowerCase() || '',
        material: document.getElementById('material-filter')?.value || '',
        thickness: document.getElementById('thickness-filter')?.value.trim().toLowerCase() || '',
        notes: document.getElementById('notes-filter')?.value.trim().toLowerCase() || '',
        shapeCode: document.getElementById('shape-code-filter')?.value.trim().toLowerCase() || '',
        process1: document.getElementById('process1-filter')?.value.trim().toLowerCase() || '',
        process2: document.getElementById('process2-filter')?.value.trim().toLowerCase() || '',
        process3: document.getElementById('process3-filter')?.value.trim().toLowerCase() || '',
        process4: document.getElementById('process4-filter')?.value.trim().toLowerCase() || '',
        carBrand: document.getElementById('car-brand-filter')?.value || '',
        smartSearch: document.getElementById('smart-search-input')?.value.trim().toLowerCase() || ''
    };

    console.log('应用筛选条件:', filters);

    // 筛选产品
    filteredProducts = allProducts.filter(product => {
        // 产品编码筛选（支持多种字段名）
        const productCodeMatch = !filters.productCode ||
            (product.stock_code && product.stock_code.toLowerCase().includes(filters.productCode)) ||
            (product.inventory_code && product.inventory_code.toLowerCase().includes(filters.productCode)) ||
            (product.data_id && product.data_id.toLowerCase().includes(filters.productCode));

        // 产品名称筛选
        const productNameMatch = !filters.productName ||
            (product.product_name && product.product_name.toLowerCase().includes(filters.productName));

        // 产品类别筛选
        const categoryMatch = !filters.category || product.product_category === filters.category;

        // 产品规格筛选（支持多种字段名）
        const specificationsMatch = !filters.specifications ||
            (product.specifications && product.specifications.toLowerCase().includes(filters.specifications)) ||
            (product.product_specs && product.product_specs.toLowerCase().includes(filters.specifications));

        // 材质筛选
        const materialMatch = !filters.material ||
            (product.material && product.material.includes(filters.material));

        // 料厚筛选（支持多种字段名）
        const thicknessMatch = !filters.thickness ||
            (product.material_thickness && product.material_thickness.toLowerCase().includes(filters.thickness)) ||
            (product.thickness && product.thickness.toLowerCase().includes(filters.thickness));

        // 备注筛选（支持多种字段名）
        const notesMatch = !filters.notes ||
            (product.notes && product.notes.toLowerCase().includes(filters.notes)) ||
            (product.remarks && product.remarks.toLowerCase().includes(filters.notes));

        // 外形编码筛选
        const shapeCodeMatch = !filters.shapeCode ||
            (product.shape_code && product.shape_code.toLowerCase().includes(filters.shapeCode));

        // 主要工艺筛选
        const process1Match = !filters.process1 ||
            (product.main_process_1 && product.main_process_1.toLowerCase().includes(filters.process1));
        const process2Match = !filters.process2 ||
            (product.main_process_2 && product.main_process_2.toLowerCase().includes(filters.process2));
        const process3Match = !filters.process3 ||
            (product.main_process_3 && product.main_process_3.toLowerCase().includes(filters.process3));
        const process4Match = !filters.process4 ||
            (product.main_process_4 && product.main_process_4.toLowerCase().includes(filters.process4));

        // 车型筛选（支持逗号分隔的多个车型）
        const carBrandMatch = !filters.carBrand ||
            (product.car_models && product.car_models.toLowerCase().includes(filters.carBrand.toLowerCase())) ||
            (product.car_model && product.car_model.toLowerCase().includes(filters.carBrand.toLowerCase())) ||
            (product.car_type && product.car_type.toLowerCase().includes(filters.carBrand.toLowerCase())) ||
            (product.product_name && product.product_name.toLowerCase().includes(filters.carBrand.toLowerCase()));

        // 智能搜索（在所有字段中搜索）
        const smartSearchMatch = !filters.smartSearch ||
            productCodeMatch || productNameMatch || specificationsMatch ||
            (product.material && product.material.toLowerCase().includes(filters.smartSearch)) ||
            (product.material_thickness && product.material_thickness.toLowerCase().includes(filters.smartSearch)) ||
            (product.notes && product.notes.toLowerCase().includes(filters.smartSearch)) ||
            (product.shape_code && product.shape_code.toLowerCase().includes(filters.smartSearch)) ||
            (product.main_process_1 && product.main_process_1.toLowerCase().includes(filters.smartSearch)) ||
            (product.main_process_2 && product.main_process_2.toLowerCase().includes(filters.smartSearch)) ||
            (product.main_process_3 && product.main_process_3.toLowerCase().includes(filters.smartSearch)) ||
            (product.main_process_4 && product.main_process_4.toLowerCase().includes(filters.smartSearch)) ||
            (product.car_models && product.car_models.toLowerCase().includes(filters.smartSearch)) ||
            (product.car_model && product.car_model.toLowerCase().includes(filters.smartSearch)) ||
            (product.car_type && product.car_type.toLowerCase().includes(filters.smartSearch));

        // 如果有智能搜索，只使用智能搜索结果
        if (filters.smartSearch) {
            return smartSearchMatch;
        }

        // 否则，所有条件都必须匹配
        return productCodeMatch && productNameMatch && categoryMatch &&
               specificationsMatch && materialMatch && thicknessMatch &&
               notesMatch && shapeCodeMatch && process1Match &&
               process2Match && process3Match && process4Match && carBrandMatch;
    });

    console.log('筛选结果:', filteredProducts.length, '个产品');

    // 显示筛选结果
    displayFilteredProducts();
}

// 重置筛选
function resetFilters() {
    // 重置所有筛选条件
    const filterIds = [
        'product-code-filter',
        'product-name-filter',
        'category-filter',
        'specifications-filter',
        'material-filter',
        'thickness-filter',
        'notes-filter',
        'shape-code-filter',
        'process1-filter',
        'process2-filter',
        'process3-filter',
        'process4-filter',
        'car-brand-filter',
        'smart-search-input'
    ];

    filterIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.value = '';
        }
    });

    // 重置筛选结果
    filteredProducts = [...allProducts];
    displayFilteredProducts();

    console.log('已重置所有筛选条件');
}

// 显示筛选后的产品
function displayFilteredProducts() {
    const container = document.getElementById('products-table-body');
    if (!container) {
        console.error('产品表格容器未找到');
        return;
    }

    console.log('开始显示产品，筛选后的产品数量:', filteredProducts.length);

    if (filteredProducts.length === 0) {
        container.innerHTML = `
            <tr>
                <td colspan="14" style="padding: 40px; text-align: center; color: #666;">
                    没有找到符合条件的产品
                </td>
            </tr>
        `;
        return;
    }

    // 生成产品表格行HTML
    console.log('生成产品表格行...');
    const productsHTML = filteredProducts.map(product => {
        console.log('处理产品:', product.product_name);
        return createProductTableRow(product);
    }).join('');

    console.log('设置产品HTML到表格容器');
    container.innerHTML = productsHTML;

    // 更新结果统计
    updateResultsCount();
}

// 创建产品表格行
function createProductTableRow(product) {
    const defaultImage = 'placeholder.svg';

    // 获取产品分类名称
    const categoryMapping = {
        'ZJ': '支架（座）类',
        'GD': '固定圈（防护套）类',
        'ZE': '支耳（板）类',
        'TP': '弹簧盘类',
        'FC': '防尘盖（顶板）类',
        'QT': '其它类',
        'DB': '防尘盖（顶板）类',
        'ZC': '支架（座）类'
    };

    return `
        <tr style="border-bottom: 1px solid #eee; font-size: 11px;" onmouseover="this.style.background='#f9f9f9'" onmouseout="this.style.background='white'">
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.stock_code || product.inventory_code || product.data_id || '无编码'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.product_name || '未知产品'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${categoryMapping[product.product_category] || product.product_category || '未分类'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.specifications || product.product_specs || '无规格'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.material || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.material_thickness || product.thickness || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.notes || product.remarks || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.car_models || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.shape_code || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.main_process_1 || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.main_process_2 || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.main_process_3 || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                ${product.main_process_4 || '无'}
            </td>
            <td style="padding: 6px; border-right: 1px solid #eee; text-align: center;">
                <img src="${product.product_image || defaultImage}"
                     alt="${product.product_name}"
                     style="width: 40px; height: 40px; object-fit: contain; cursor: pointer; border: 1px solid #ddd;"
                     onclick="showImageModal('${product.product_image || defaultImage}', '${product.product_name}')"
                     onerror="this.src='${defaultImage}'">
            </td>
            <td style="padding: 6px; text-align: center;">
                <button onclick="viewProductDetail('${product.id}')"
                        style="padding: 4px 8px; background: #be131b; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">
                    详情
                </button>
            </td>
        </tr>
    `;
}

// 显示图片模态框
function showImageModal(imageSrc, productName) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        cursor: pointer;
    `;

    modal.innerHTML = `
        <div style="max-width: 90%; max-height: 90%; background: white; padding: 20px; border-radius: 8px;">
            <h3 style="margin: 0 0 15px 0; text-align: center;">${productName}</h3>
            <img src="${imageSrc}" alt="${productName}" style="max-width: 100%; max-height: 70vh; object-fit: contain;">
            <p style="text-align: center; margin-top: 15px; color: #666;">点击任意位置关闭</p>
        </div>
    `;

    modal.onclick = function() {
        document.body.removeChild(modal);
    };

    document.body.appendChild(modal);
}

// 查看产品详情
function viewProductDetail(productId) {
    window.location.href = `product-detail.html?id=${productId}`;
}

// 导出查询结果
function exportResults() {
    if (filteredProducts.length === 0) {
        alert('没有查询结果可以导出');
        return;
    }

    // 创建CSV内容
    const headers = [
        '产品编码', '产品名称', '产品类别', '产品规格', '材质', '料厚',
        '备注', '外形编码', '主要工艺1', '主要工艺2', '主要工艺3', '主要工艺4'
    ];

    let csvContent = headers.join(',') + '\n';

    filteredProducts.forEach(product => {
        const row = [
            product.stock_code || product.inventory_code || product.data_id || '',
            product.product_name || '',
            product.product_category || '',
            product.specifications || product.product_specs || '',
            product.material || '',
            product.material_thickness || product.thickness || '',
            product.notes || product.remarks || '',
            product.shape_code || '',
            product.main_process_1 || '',
            product.main_process_2 || '',
            product.main_process_3 || '',
            product.main_process_4 || ''
        ];

        // 处理包含逗号的字段，用引号包围
        const processedRow = row.map(field => {
            if (field.includes(',') || field.includes('"')) {
                return '"' + field.replace(/"/g, '""') + '"';
            }
            return field;
        });

        csvContent += processedRow.join(',') + '\n';
    });

    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `产品查询结果_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('导出了', filteredProducts.length, '个产品的查询结果');
}

// 全局函数：在表格中显示产品（供main.js调用）
window.displayProductsInTable = function(products) {
    allProducts = products || [];
    filteredProducts = [...allProducts];
    displayFilteredProducts();
};

// 更新结果统计
function updateResultsCount() {
    // 更新搜索结果统计
    const searchCountDisplay = document.getElementById('search-count-display');
    const currentPageDisplay = document.getElementById('current-page-display');
    const totalPagesDisplay = document.getElementById('total-pages-display');

    if (searchCountDisplay) {
        searchCountDisplay.textContent = filteredProducts.length;
    }

    if (currentPageDisplay) {
        currentPageDisplay.textContent = '1';
    }

    if (totalPagesDisplay) {
        const totalPages = Math.ceil(filteredProducts.length / 20); // 假设每页20个产品
        totalPagesDisplay.textContent = totalPages || 1;
    }

    // 可以在页面上显示筛选结果数量
    const totalCount = allProducts.length;
    const filteredCount = filteredProducts.length;
    
    // 如果有结果统计容器，更新它
    const statsContainer = document.getElementById('results-stats');
    if (statsContainer) {
        statsContainer.textContent = `显示 ${filteredCount} / ${totalCount} 个产品`;
    }
}

// 产品排序功能
function sortProducts(sortBy) {
    switch (sortBy) {
        case 'name':
            filteredProducts.sort((a, b) => a.product_name.localeCompare(b.product_name));
            break;
        case 'category':
            filteredProducts.sort((a, b) => a.product_category.localeCompare(b.product_category));
            break;
        case 'date':
            filteredProducts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            break;
        default:
            break;
    }
    
    displayFilteredProducts();
}

// 导出产品数据（管理员功能）
function exportProducts() {
    if (!currentUser || currentUserType !== 'admin') {
        alert('只有管理员可以导出产品数据');
        return;
    }
    
    // 创建CSV数据
    const headers = [
        '数据ID', '存货编码', '产品名称', '产品类别', '产品规格', '材质', '料厚',
        '备注', '外形编码', '主要工艺1', '主要工艺2', '主要工艺3', '主要工艺4',
        '工序数', '可变加工艺1', '可变加工艺2', '可变加工艺3'
    ];
    
    const csvContent = [
        headers.join(','),
        ...filteredProducts.map(product => [
            product.data_id,
            product.stock_code,
            product.product_name,
            product.product_category,
            product.specifications,
            product.material,
            product.thickness,
            product.remarks || '',
            product.shape_code || '',
            product.main_process_1 || '',
            product.main_process_2 || '',
            product.main_process_3 || '',
            product.main_process_4 || '',
            product.process_count || '',
            product.variable_process_1 || '',
            product.variable_process_2 || '',
            product.variable_process_3 || ''
        ].map(field => `"${field}"`).join(','))
    ].join('\n');
    
    // 下载CSV文件
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `产品数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 动态加载材质选项
function loadMaterialOptions() {
    try {
        const materialFilter = document.getElementById('material-filter');
        if (!materialFilter) {
            console.log('材质筛选器不存在，跳过材质选项加载');
            return;
        }

        // 从产品数据中提取所有材质
        const materials = new Set();
        allProducts.forEach(product => {
            if (product.material && product.material.trim()) {
                // 处理可能包含多个材质的情况（用逗号分隔）
                const materialList = product.material.split(',').map(m => m.trim());
                materialList.forEach(material => {
                    if (material) materials.add(material);
                });
            }
        });

        // 保存当前选中的值
        const currentValue = materialFilter.value;

        // 清空现有选项（保留"所有材质"选项）
        materialFilter.innerHTML = '<option value="">所有材质</option>';

        // 添加从数据库中提取的材质选项
        const sortedMaterials = Array.from(materials).sort();
        sortedMaterials.forEach(material => {
            const option = document.createElement('option');
            option.value = material;
            option.textContent = material;
            materialFilter.appendChild(option);
        });

        // 恢复之前选中的值
        if (currentValue && materials.has(currentValue)) {
            materialFilter.value = currentValue;
        }

        console.log('材质选项已更新，共', materials.size, '种材质');
    } catch (error) {
        console.error('加载材质选项失败:', error);
    }
}

// 批量操作功能（管理员）
function initializeBatchOperations() {
    if (!currentUser || currentUserType !== 'admin') return;
    
    // 添加批量操作按钮
    const container = document.querySelector('.product-filters .filter-controls');
    if (container) {
        const batchHTML = `
            <div class="batch-operations" style="margin-left: auto; display: flex; gap: 10px;">
                <button id="select-all-btn" class="btn btn-secondary">全选</button>
                <button id="export-btn" class="btn">导出数据</button>
                <button id="batch-delete-btn" class="btn" style="background: #dc3545;">批量删除</button>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', batchHTML);
        
        // 添加事件监听
        document.getElementById('select-all-btn').addEventListener('click', toggleSelectAll);
        document.getElementById('export-btn').addEventListener('click', exportProducts);
        document.getElementById('batch-delete-btn').addEventListener('click', batchDeleteProducts);
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.product-checkbox');
    const selectAllBtn = document.getElementById('select-all-btn');
    
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
    
    selectAllBtn.textContent = allChecked ? '全选' : '取消全选';
}

// 批量删除产品
async function batchDeleteProducts() {
    const selectedIds = Array.from(document.querySelectorAll('.product-checkbox:checked'))
        .map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('请选择要删除的产品');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedIds.length} 个产品吗？此操作不可恢复。`)) {
        return;
    }
    
    try {
        const { error } = await supabase
            .from('products')
            .delete()
            .in('id', selectedIds);
        
        if (error) throw error;
        
        alert('删除成功');
        await loadAllProductsData();
        displayFilteredProducts();
    } catch (error) {
        console.error('删除失败:', error);
        alert('删除失败，请稍后重试');
    }
}
