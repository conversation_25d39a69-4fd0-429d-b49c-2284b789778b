-- 为 products 表添加 car_models 字段
-- 用于存储产品适用的车型信息，支持逗号分隔的多个车型

-- 添加 car_models 字段
ALTER TABLE products ADD COLUMN IF NOT EXISTS car_models TEXT;

-- 添加字段注释
COMMENT ON COLUMN products.car_models IS '适用车型，多个车型用逗号分隔';

-- 更新一些示例数据（可选）
UPDATE products SET car_models = '奥迪A4L,宝马3系' WHERE data_id = 'A0001';
UPDATE products SET car_models = '奔驰C级,奥迪A6L' WHERE data_id = 'A0002';
UPDATE products SET car_models = '大众帕萨特,丰田凯美瑞' WHERE data_id = 'A0003';
UPDATE products SET car_models = '本田雅阁,日产天籁' WHERE data_id = 'A0004';
UPDATE products SET car_models = '现代索纳塔,起亚K5' WHERE data_id = 'A0005';

-- 创建索引以提高查询性能（可选）
CREATE INDEX IF NOT EXISTS idx_products_car_models ON products USING gin(to_tsvector('simple', car_models));

-- 说明：
-- 1. car_models 字段用于存储产品适用的车型信息
-- 2. 多个车型用逗号分隔，例如："奥迪A4L,宝马3系,奔驰C级"
-- 3. 查询时支持字符串匹配，可以搜索任何包含的车型关键词
-- 4. 创建了全文搜索索引以提高查询性能
