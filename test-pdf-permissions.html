<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF权限测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #be131b;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #a00e15;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .permission-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .permission-table th, .permission-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .permission-table th {
            background: #f8f9fa;
        }
        .allowed { color: #28a745; font-weight: bold; }
        .denied { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🧪 PDF下载权限测试</h1>
    
    <div class="test-section">
        <h2>权限矩阵</h2>
        <table class="permission-table">
            <thead>
                <tr>
                    <th>用户类型</th>
                    <th>查看详情</th>
                    <th>下载基础资料</th>
                    <th>下载PDF文档</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>游客 (guest)</td>
                    <td class="denied">❌ 拒绝</td>
                    <td class="denied">❌ 拒绝</td>
                    <td class="denied">❌ 拒绝</td>
                </tr>
                <tr>
                    <td>普通用户 (normal)</td>
                    <td class="denied">❌ 拒绝</td>
                    <td class="denied">❌ 拒绝</td>
                    <td class="denied">❌ 拒绝</td>
                </tr>
                <tr>
                    <td>高级用户 (premium)</td>
                    <td class="allowed">✅ 允许</td>
                    <td class="allowed">✅ 允许</td>
                    <td class="allowed">✅ 允许 (新增)</td>
                </tr>
                <tr>
                    <td>特许用户 (privileged)</td>
                    <td class="allowed">✅ 允许</td>
                    <td class="allowed">✅ 允许</td>
                    <td class="allowed">✅ 允许</td>
                </tr>
                <tr>
                    <td>管理员 (admin)</td>
                    <td class="allowed">✅ 允许</td>
                    <td class="allowed">✅ 允许</td>
                    <td class="allowed">✅ 允许</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-section">
        <h2>模拟用户权限测试</h2>
        <div class="form-group">
            <label for="userType">选择用户类型:</label>
            <select id="userType">
                <option value="guest">游客 (guest)</option>
                <option value="normal">普通用户 (normal)</option>
                <option value="premium" selected>高级用户 (premium)</option>
                <option value="privileged">特许用户 (privileged)</option>
                <option value="admin">管理员 (admin)</option>
            </select>
        </div>
        <button onclick="testPermissions()">测试权限</button>
        <div id="permission-result"></div>
    </div>

    <div class="test-section">
        <h2>实际用户权限检查</h2>
        <button onclick="checkCurrentUser()">检查当前用户权限</button>
        <div id="current-user-result"></div>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // 权限检查函数（模拟）
        function checkPermissions(userType) {
            const permissions = {
                canViewDetails: ['premium', 'privileged', 'admin'].includes(userType),
                canDownloadBasic: ['premium', 'privileged', 'admin'].includes(userType),
                canDownloadPDF: ['premium', 'privileged', 'admin'].includes(userType) // 新增：高级用户也可以下载PDF
            };
            return permissions;
        }

        // 测试模拟权限
        function testPermissions() {
            const userType = document.getElementById('userType').value;
            const permissions = checkPermissions(userType);
            
            const resultHtml = `
                <div class="success">✅ 权限测试结果 - ${userType}</div>
                <table class="permission-table">
                    <tr>
                        <td>查看详情</td>
                        <td class="${permissions.canViewDetails ? 'allowed' : 'denied'}">
                            ${permissions.canViewDetails ? '✅ 允许' : '❌ 拒绝'}
                        </td>
                    </tr>
                    <tr>
                        <td>下载基础资料</td>
                        <td class="${permissions.canDownloadBasic ? 'allowed' : 'denied'}">
                            ${permissions.canDownloadBasic ? '✅ 允许' : '❌ 拒绝'}
                        </td>
                    </tr>
                    <tr>
                        <td>下载PDF文档</td>
                        <td class="${permissions.canDownloadPDF ? 'allowed' : 'denied'}">
                            ${permissions.canDownloadPDF ? '✅ 允许' : '❌ 拒绝'}
                        </td>
                    </tr>
                </table>
            `;
            
            showResult('permission-result', resultHtml, 'success');
        }

        // 检查当前实际用户权限
        async function checkCurrentUser() {
            try {
                // 等待认证系统初始化
                await new Promise(resolve => {
                    if (window.auth && window.auth.isInitialized) {
                        resolve();
                    } else {
                        window.addEventListener('authReady', resolve, { once: true });
                    }
                });

                const userType = window.auth ? window.auth.getUserType() : 'guest';
                const currentUser = window.auth ? window.auth.getCurrentUser() : null;

                let resultHtml = `
                    <div class="info">📊 当前用户权限状态</div>
                    <pre>${JSON.stringify({
                        用户类型: userType,
                        用户名: currentUser ? currentUser.username : '未登录',
                        邮箱: currentUser ? currentUser.email : '未登录'
                    }, null, 2)}</pre>
                `;

                if (window.auth && window.auth.isInitialized) {
                    const permissions = {
                        canViewDetails: window.auth.canViewDetails(),
                        canDownloadBasic: window.auth.canDownloadBasic(),
                        canDownloadAll: window.auth.canDownloadAll()
                    };

                    resultHtml += `
                        <table class="permission-table">
                            <tr>
                                <td>查看详情</td>
                                <td class="${permissions.canViewDetails ? 'allowed' : 'denied'}">
                                    ${permissions.canViewDetails ? '✅ 允许' : '❌ 拒绝'}
                                </td>
                            </tr>
                            <tr>
                                <td>下载基础资料</td>
                                <td class="${permissions.canDownloadBasic ? 'allowed' : 'denied'}">
                                    ${permissions.canDownloadBasic ? '✅ 允许' : '❌ 拒绝'}
                                </td>
                            </tr>
                            <tr>
                                <td>下载PDF文档</td>
                                <td class="${permissions.canDownloadAll ? 'allowed' : 'denied'}">
                                    ${permissions.canDownloadAll ? '✅ 允许' : '❌ 拒绝'}
                                </td>
                            </tr>
                        </table>
                    `;
                } else {
                    resultHtml += `<div class="warning">⚠️ 认证系统未初始化</div>`;
                }

                showResult('current-user-result', resultHtml, 'info');

            } catch (error) {
                console.error('检查用户权限失败:', error);
                showResult('current-user-result', `❌ 检查失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查当前用户
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkCurrentUser();
            }, 2000);
        });
    </script>
</body>
</html>
