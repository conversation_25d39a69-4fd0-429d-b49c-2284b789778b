@echo off
chcp 65001 >nul
echo ========================================
echo          产品文件重命名工具
echo ========================================
echo.
echo 此工具将帮助您批量重命名图片和PDF文件
echo 重命名格式：
echo   图片: A0014.jpg, A0015.png 等
echo   PDF:  A0014_技术文档.pdf, A0015_说明书.pdf 等
echo.

set /p source_dir="请输入文件所在目录路径（直接回车使用当前目录）: "
if "%source_dir%"=="" set source_dir=.

if not exist "%source_dir%" (
    echo 错误：目录 %source_dir% 不存在
    pause
    exit /b
)

echo.
echo 开始处理目录: %source_dir%
echo.

cd /d "%source_dir%"

:: 数据ID数组
set data_ids=A0014 A0015 A0016 A0017 A0018 A0004 A0005 A0006 A0007 A0008 A0009 A0010 A0011 A0012 A0013

:: PDF后缀数组
set pdf_suffixes=技术文档 说明书 产品手册 工艺文档 检验标准

echo === 重命名图片文件 ===
set img_count=0
for %%f in (*.jpg *.jpeg *.png *.gif *.bmp) do (
    set /a img_count+=1
    call :rename_image "%%f" !img_count!
)

echo.
echo === 重命名PDF文件 ===
set pdf_count=0
for %%f in (*.pdf) do (
    set /a pdf_count+=1
    call :rename_pdf "%%f" !pdf_count!
)

echo.
echo === 重命名完成 ===
echo 重命名后的文件已保存在当前目录中
echo 原文件保持不变
echo.
pause
exit /b

:rename_image
setlocal enabledelayedexpansion
set file=%~1
set index=%2

:: 获取对应的数据ID
set count=0
for %%i in (%data_ids%) do (
    set /a count+=1
    if !count! equ %index% (
        set data_id=%%i
        goto :found_id
    )
)
echo ⚠️  跳过 %file% (数据ID不足)
goto :eof

:found_id
:: 获取文件扩展名
for %%i in (%file%) do set ext=%%~xi
set new_name=!data_id!!ext!

copy "%file%" "!new_name!" >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ %file% -^> !new_name!
) else (
    echo ❌ 重命名失败 %file%
)
endlocal
goto :eof

:rename_pdf
setlocal enabledelayedexpansion
set file=%~1
set index=%2

:: 获取对应的数据ID
set count=0
for %%i in (%data_ids%) do (
    set /a count+=1
    if !count! equ %index% (
        set data_id=%%i
        goto :found_pdf_id
    )
)
echo ⚠️  跳过 %file% (数据ID不足)
goto :eof

:found_pdf_id
:: 获取对应的PDF后缀
set suffix_count=0
for %%s in (%pdf_suffixes%) do (
    set /a suffix_count+=1
    set /a mod_result=%index% %% 5
    if !mod_result! equ 0 set mod_result=5
    if !suffix_count! equ !mod_result! (
        set pdf_suffix=%%s
        goto :found_suffix
    )
)

:found_suffix
set new_name=!data_id!_!pdf_suffix!.pdf

copy "%file%" "!new_name!" >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ %file% -^> !new_name!
) else (
    echo ❌ 重命名失败 %file%
)
endlocal
goto :eof
