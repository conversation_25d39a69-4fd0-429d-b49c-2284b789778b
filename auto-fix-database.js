// 自动修复数据库字段的脚本
// 在浏览器控制台中运行此脚本

async function autoFixDatabase() {
    console.log('🔧 开始自动修复数据库字段...');
    
    try {
        // 1. 检查当前字段
        console.log('📋 步骤1: 检查当前字段');
        const { data: currentData, error: checkError } = await supabase
            .from('products')
            .select('*')
            .limit(1);

        if (checkError) {
            console.error('❌ 检查字段失败:', checkError);
            return;
        }

        if (!currentData || currentData.length === 0) {
            console.log('⚠️ 数据库中没有产品数据，无法检查字段');
            return;
        }

        const existingFields = Object.keys(currentData[0]);
        console.log('✅ 当前字段:', existingFields);

        // 2. 定义需要的字段
        const requiredFields = {
            'material_thickness': 'TEXT',
            'notes': 'TEXT',
            'shape_code': 'TEXT',
            'main_process_1': 'TEXT',
            'main_process_2': 'TEXT',
            'main_process_3': 'TEXT',
            'main_process_4': 'TEXT',
            'process_count': 'INTEGER',
            'variable_process_1': 'TEXT',
            'variable_process_2': 'TEXT',
            'variable_process_3': 'TEXT',
            'product_image': 'TEXT',
            'user_access_level': 'TEXT',
            'inventory_code': 'TEXT',
            'product_specs': 'TEXT',
            'thickness': 'TEXT',
            'remarks': 'TEXT'
        };

        // 3. 检查缺失字段
        const missingFields = Object.keys(requiredFields).filter(field => !existingFields.includes(field));
        console.log('📝 缺失字段:', missingFields);

        if (missingFields.length === 0) {
            console.log('✅ 所有字段都存在！');
        } else {
            console.log('⚠️ 需要添加字段，请在Supabase控制台执行以下SQL:');
            missingFields.forEach(field => {
                const sql = `ALTER TABLE products ADD COLUMN IF NOT EXISTS ${field} ${requiredFields[field]};`;
                console.log(sql);
            });
        }

        // 4. 检查数据映射
        console.log('\n📊 步骤2: 检查数据映射');
        const { data: allProducts, error: dataError } = await supabase
            .from('products')
            .select('*');

        if (dataError) {
            console.error('❌ 获取产品数据失败:', dataError);
            return;
        }

        console.log(`📦 总共 ${allProducts.length} 个产品`);

        // 5. 分析字段使用情况
        const fieldUsage = {};
        allProducts.forEach(product => {
            Object.keys(product).forEach(field => {
                if (!fieldUsage[field]) fieldUsage[field] = 0;
                if (product[field] !== null && product[field] !== '') {
                    fieldUsage[field]++;
                }
            });
        });

        console.log('\n📈 字段使用情况:');
        Object.entries(fieldUsage).forEach(([field, count]) => {
            console.log(`${field}: ${count}/${allProducts.length} (${Math.round(count/allProducts.length*100)}%)`);
        });

        // 6. 检查字段映射需求
        console.log('\n🔄 步骤3: 检查字段映射需求');
        const mappingNeeds = [];

        allProducts.forEach((product, index) => {
            // 检查thickness -> material_thickness
            if (product.thickness && !product.material_thickness) {
                mappingNeeds.push({
                    id: product.id,
                    action: 'copy thickness to material_thickness',
                    from: 'thickness',
                    to: 'material_thickness',
                    value: product.thickness
                });
            }

            // 检查remarks -> notes
            if (product.remarks && !product.notes) {
                mappingNeeds.push({
                    id: product.id,
                    action: 'copy remarks to notes',
                    from: 'remarks',
                    to: 'notes',
                    value: product.remarks
                });
            }

            // 检查product_specs -> specifications
            if (product.product_specs && !product.specifications) {
                mappingNeeds.push({
                    id: product.id,
                    action: 'copy product_specs to specifications',
                    from: 'product_specs',
                    to: 'specifications',
                    value: product.product_specs
                });
            }

            // 检查inventory_code -> stock_code
            if (product.inventory_code && !product.stock_code) {
                mappingNeeds.push({
                    id: product.id,
                    action: 'copy inventory_code to stock_code',
                    from: 'inventory_code',
                    to: 'stock_code',
                    value: product.inventory_code
                });
            }
        });

        console.log(`🔄 需要映射的数据: ${mappingNeeds.length} 项`);
        mappingNeeds.forEach(need => {
            console.log(`- 产品 ${need.id}: ${need.action} (${need.value})`);
        });

        // 7. 执行数据映射（如果用户确认）
        if (mappingNeeds.length > 0) {
            console.log('\n❓ 是否执行数据映射？在控制台输入: executeMappings()');
            
            // 将映射需求保存到全局变量
            window.pendingMappings = mappingNeeds;
        }

        console.log('\n✅ 数据库字段检查完成！');
        
        return {
            existingFields,
            missingFields,
            fieldUsage,
            mappingNeeds
        };

    } catch (error) {
        console.error('❌ 自动修复失败:', error);
    }
}

// 执行数据映射的函数
async function executeMappings() {
    if (!window.pendingMappings || window.pendingMappings.length === 0) {
        console.log('❌ 没有待执行的映射');
        return;
    }

    console.log('🔄 开始执行数据映射...');
    
    let successCount = 0;
    let errorCount = 0;

    for (const mapping of window.pendingMappings) {
        try {
            const updateData = {};
            updateData[mapping.to] = mapping.value;

            const { error } = await supabase
                .from('products')
                .update(updateData)
                .eq('id', mapping.id);

            if (error) {
                console.error(`❌ 映射失败 ${mapping.id}:`, error);
                errorCount++;
            } else {
                console.log(`✅ 映射成功 ${mapping.id}: ${mapping.action}`);
                successCount++;
            }
        } catch (error) {
            console.error(`❌ 映射异常 ${mapping.id}:`, error);
            errorCount++;
        }
    }

    console.log(`\n📊 映射完成: 成功 ${successCount}, 失败 ${errorCount}`);
    
    // 清除待执行的映射
    window.pendingMappings = null;
}

// 验证修复结果的函数
async function verifyFix() {
    console.log('🔍 验证修复结果...');
    
    try {
        const { data, error } = await supabase
            .from('products')
            .select('*')
            .limit(3);

        if (error) {
            console.error('❌ 验证失败:', error);
            return;
        }

        console.log(`✅ 验证成功！获取到 ${data.length} 个产品`);
        
        if (data.length > 0) {
            const product = data[0];
            console.log('📋 第一个产品的字段:');
            console.log(Object.keys(product));
            
            console.log('\n📝 关键字段值:');
            const keyFields = ['stock_code', 'inventory_code', 'product_name', 'specifications', 'product_specs', 'material', 'material_thickness', 'thickness', 'notes', 'remarks'];
            keyFields.forEach(field => {
                if (product.hasOwnProperty(field)) {
                    console.log(`${field}: ${product[field] || '(空)'}`);
                }
            });
        }
        
    } catch (error) {
        console.error('❌ 验证异常:', error);
    }
}

// 导出函数到全局作用域
window.autoFixDatabase = autoFixDatabase;
window.executeMappings = executeMappings;
window.verifyFix = verifyFix;

console.log('🚀 数据库修复脚本已加载！');
console.log('📝 使用方法:');
console.log('1. autoFixDatabase() - 检查和分析数据库');
console.log('2. executeMappings() - 执行数据映射');
console.log('3. verifyFix() - 验证修复结果');
