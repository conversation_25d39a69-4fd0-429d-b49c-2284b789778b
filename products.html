<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品中心 - 安徽春晟机械有限公司</title>
    <meta name="description" content="安徽春晟机械有限公司产品中心，专业生产各类减震器冲压件产品">
    <meta name="keywords" content="减震器冲压件,汽车零部件,精密冲压件">
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- 搜索框增强样式 -->
    <style>
        /* 修复搜索框文字显示问题 */
        #smart-search-input {
            color: #333 !important;
            background: white !important;
        }

        #smart-search-input::placeholder {
            color: #666 !important;
            opacity: 1 !important;
        }

        /* 搜索框悬停效果 */
        #smart-search-input:hover {
            border-color: #d32f2f !important;
            box-shadow: 0 0 0 3px rgba(190, 19, 27, 0.1) !important;
        }

        #smart-search-input:focus {
            color: #333 !important;
            background: white !important;
        }

        #smart-search-input:focus {
            border-color: #be131b !important;
            box-shadow: 0 0 0 4px rgba(190, 19, 27, 0.15) !important;
        }

        /* 搜索按钮悬停效果 */
        #smart-search-btn:hover {
            background: linear-gradient(135deg, #d32f2f 0%, #be131b 100%) !important;
            transform: translateY(-50%) scale(1.05) !important;
            box-shadow: 0 6px 20px rgba(190, 19, 27, 0.4) !important;
        }

        /* 选项卡悬停效果 */
        .search-options label:hover,
        .filter-options > div:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.12) !important;
        }

        /* 下拉框样式优化 */
        #tolerance-select,
        #category-filter,
        #material-filter {
            color: #333 !important;
            font-weight: 500 !important;
        }

        #tolerance-select option,
        #category-filter option,
        #material-filter option {
            color: #333 !important;
            background: white !important;
            padding: 8px !important;
        }

        /* 重置按钮悬停效果 */
        #reset-filters-btn:hover {
            background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
        }

        /* 搜索历史标签样式 */
        .history-tag {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border: 2px solid #dee2e6 !important;
            padding: 6px 12px !important;
            border-radius: 15px !important;
            font-size: 13px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            font-weight: 500 !important;
            color: #333 !important;
            text-decoration: none !important;
            display: inline-block !important;
        }

        .history-tag:hover {
            background: linear-gradient(135deg, #be131b 0%, #d32f2f 100%) !important;
            color: white !important;
            border-color: #be131b !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(190, 19, 27, 0.3);
        }

        /* 动画效果 */
        @keyframes searchPulse {
            0% { box-shadow: 0 0 0 0 rgba(190, 19, 27, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(190, 19, 27, 0); }
            100% { box-shadow: 0 0 0 0 rgba(190, 19, 27, 0); }
        }

        .search-pulse {
            animation: searchPulse 1.5s infinite;
        }
    </style>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: white; background: #be131b; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 页面标题 -->
    <section class="page-header" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('16719971.jpg') center/cover; padding: 80px 0; color: white; text-align: center;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <h1 style="font-size: 48px; margin-bottom: 20px;">产品中心</h1>
            <p style="font-size: 18px;">专业生产各类减震器冲压件产品</p>
        </div>
    </section>



    <!-- 智能搜索 -->
    <section class="smart-search-section" style="background: #f5f5f5; padding: 40px 0;">
        <div class="container" style="width: 1000px; margin: 0 auto;">
            <!-- 搜索标题 -->
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="font-size: 24px; color: #333; margin-bottom: 8px; font-weight: 600;">🔍 智能产品搜索</h2>
                <p style="color: #666; font-size: 14px;">支持特殊符号、近似数值匹配，快速找到您需要的产品</p>
            </div>

            <!-- 主搜索框 -->
            <div class="main-search" style="text-align: center; margin-bottom: 25px;">
                <div style="position: relative; display: inline-block; width: 100%; max-width: 600px; border: 2px solid #be131b; border-radius: 25px; background: white;">
                    <input type="text" id="smart-search-input" placeholder="智能搜索：支持φ78、74±2等特殊符号和近似数值..."
                           style="width: calc(100% - 80px); padding: 12px 20px; border: none; border-radius: 25px; font-size: 14px; outline: none; background: transparent;">
                    <button id="smart-search-btn" style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); background: #be131b; color: white; border: none; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 14px;">
                        搜索
                    </button>
                </div>
            </div>

            <!-- 搜索选项 -->
            <div class="search-options" style="display: flex; justify-content: center; gap: 20px; margin-bottom: 25px; flex-wrap: wrap;">
                <label style="display: flex; align-items: center; gap: 5px; cursor: pointer;">
                    <input type="checkbox" id="include-approximate" checked style="width: 16px; height: 16px; accent-color: #be131b;">
                    <span style="font-size: 14px; color: #333;">包含近似匹配</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-size: 14px; color: #333;">误差范围：</span>
                    <select id="tolerance-select" style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; background: white; color: #333; font-size: 14px;">
                        <option value="1">±1</option>
                        <option value="2">±2</option>
                        <option value="3" selected>±3</option>
                        <option value="5">±5</option>
                        <option value="10">±10</option>
                    </select>
                </label>
            </div>

            <!-- 详细查询条件框 -->
            <div style="background: white; border: 2px solid #be131b; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
                <h4 style="margin: 0 0 15px 0; color: #be131b; font-size: 16px;">详细查询条件</h4>

                <!-- 第一行：基本信息 -->
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 15px;">
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">产品编码</label>
                        <input type="text" id="product-code-filter" placeholder="输入产品编码" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">产品名称</label>
                        <input type="text" id="product-name-filter" placeholder="输入产品名称" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">产品类别</label>
                        <select id="category-filter" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="">所有类别</option>
                            <option value="ZJ">支架（座）类</option>
                            <option value="GD">固定圈（防护套）类</option>
                            <option value="ZE">支耳（板）类</option>
                            <option value="TP">弹簧盘类</option>
                            <option value="FC">防尘盖（顶板）类</option>
                            <option value="QT">其它类</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">产品规格</label>
                        <input type="text" id="specifications-filter" placeholder="输入产品规格" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                </div>

                <!-- 第二行：材质信息 -->
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 15px;">
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">材质</label>
                        <select id="material-filter" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="">所有材质</option>
                            <option value="SPHC">SPHC</option>
                            <option value="SPCC">SPCC</option>
                            <option value="Q235">Q235</option>
                            <option value="Q345">Q345</option>
                            <option value="65Mn">65Mn</option>
                            <option value="45#">45#</option>
                            <option value="20#">20#</option>
                            <option value="不锈钢">不锈钢</option>
                            <option value="铝合金">铝合金</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">料厚</label>
                        <input type="text" id="thickness-filter" placeholder="输入料厚" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">备注</label>
                        <input type="text" id="notes-filter" placeholder="输入备注关键词" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">外形编码</label>
                        <input type="text" id="shape-code-filter" placeholder="输入外形编码" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                </div>

                <!-- 第三行：工艺信息 -->
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 15px;">
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">主要工艺1</label>
                        <input type="text" id="process1-filter" placeholder="输入主要工艺1" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">主要工艺2</label>
                        <input type="text" id="process2-filter" placeholder="输入主要工艺2" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">主要工艺3</label>
                        <input type="text" id="process3-filter" placeholder="输入主要工艺3" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">主要工艺4</label>
                        <input type="text" id="process4-filter" placeholder="输入主要工艺4" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                </div>

                <!-- 第四行：车型和操作 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px; align-items: end;">
                    <div>
                        <label style="display: block; font-size: 12px; color: #666; margin-bottom: 5px;">车型</label>
                        <select id="car-brand-filter" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="">所有车型</option>
                        </select>
                    </div>
                    <div>
                        <button id="search-filters-btn" style="width: 100%; padding: 8px; background: #be131b; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">
                            🔍 查询
                        </button>
                    </div>
                    <div>
                        <button id="reset-filters-btn" style="width: 100%; padding: 8px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                            🔄 重置
                        </button>
                    </div>
                    <div>
                        <button id="export-results-btn" style="width: 100%; padding: 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                            📊 导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- 查询说明 -->
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
                <div style="color: #856404; font-size: 14px; line-height: 1.5;">
                    <strong>可直接条件查询：</strong>产品编码、产品名称、产品类别、产品规格、材质、材厚、备注、外形编码、主要工艺1、主要工艺2、主要工艺3、主要工艺4、车型等
                    <br>
                    <strong>实际查询条件：</strong>4项由用户自选，智能搜索范围为：条件筛选后
                </div>
            </div>

            <!-- 查询结果说明 -->
            <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 6px; padding: 15px;">
                <div style="color: #0c5460; font-size: 14px; line-height: 1.5;">
                    <strong>查询结果以详细列表形式显示，</strong>图片是缩略图，点击后可放大，列表内容包含：产品编码、产品名称、产品类别、产品规格、材质、材厚、备注、外形编码、主要工艺1、主要工艺2、主要工艺3、主要工艺4
                </div>
            </div>

            <!-- 搜索结果统计 -->
            <div id="search-stats" style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(190, 19, 27, 0.1); border-radius: 8px; color: #be131b; font-size: 16px; font-weight: 600; display: none;"></div>
        </div>
    </section>

    <!-- 产品列表 -->
    <section class="products-section" style="padding: 30px 0; background: #f8f8f8;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <!-- 产品表格 -->
            <div style="background: white; border: 2px solid #333; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <!-- 表格标题 -->
                <div style="background: #e6e6e6; padding: 10px; text-align: center; border-bottom: 2px solid #333;">
                    <h3 style="margin: 0; font-size: 16px; color: #333;">高级搜索（页面）</h3>
                </div>

                <!-- 搜索结果统计行 -->
                <div id="search-result-header" style="background: #f0f0f0; padding: 8px; border-bottom: 1px solid #ccc; display: flex; align-items: center; justify-content: space-between; font-size: 14px;">
                    <div style="display: flex; gap: 20px;">
                        <span>搜索结果: <strong id="search-count-display">0</strong> 个产品</span>
                        <span>当前页面: <strong id="current-page-display">1</strong></span>
                        <span>总页数: <strong id="total-pages-display">1</strong></span>
                    </div>
                </div>

                <!-- 表格头部 -->
                <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
                    <thead>
                        <tr style="background: #f8f8f8; border-bottom: 2px solid #333;">
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 8%;">产品编码</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 10%;">产品名称</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 8%;">产品类别</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 10%;">产品规格</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 6%;">材质</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 6%;">料厚</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 8%;">备注</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 10%;">适用车型</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 8%;">外形编码</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 6%;">主要工艺1</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 6%;">主要工艺2</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 6%;">主要工艺3</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 6%;">主要工艺4</th>
                            <th style="padding: 8px; border-right: 1px solid #ccc; font-size: 12px; font-weight: bold; text-align: center; width: 8%;">缩略图</th>
                            <th style="padding: 8px; font-size: 12px; font-weight: bold; text-align: center; width: 6%;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="products-table-body">
                        <!-- 产品数据将通过JavaScript动态加载 -->
                        <tr>
                            <td colspan="15" style="padding: 40px; text-align: center; color: #666;">
                                <div class="loading">
                                    <div class="spinner"></div>
                                    <p style="margin-top: 10px;">正在加载产品数据...</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- 表格底部说明 -->
                <div style="background: #f0f0f0; padding: 10px; border-top: 1px solid #ccc; font-size: 12px; color: #666;">
                    搜索页可下拉选择（可分页），以列表形式浏览符合条件产品。点击缩略图可以放大图片，点击详情可预览PDF文件；点击详情可查看产品完整资料，单页显示不完整时可选
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination" style="text-align: center; margin-top: 30px;">
                <button class="page-btn" style="padding: 8px 16px; margin: 0 5px; border: 1px solid #ccc; background: white; cursor: pointer;">上一页</button>
                <span class="page-numbers">
                    <button class="page-btn active" style="padding: 8px 12px; margin: 0 2px; border: 1px solid #be131b; background: #be131b; color: white; cursor: pointer;">1</button>
                    <button class="page-btn" style="padding: 8px 12px; margin: 0 2px; border: 1px solid #ccc; background: white; cursor: pointer;">2</button>
                    <button class="page-btn" style="padding: 8px 12px; margin: 0 2px; border: 1px solid #ccc; background: white; cursor: pointer;">3</button>
                    <button class="page-btn" style="padding: 8px 12px; margin: 0 2px; border: 1px solid #ccc; background: white; cursor: pointer;">4</button>
                    <button class="page-btn" style="padding: 8px 12px; margin: 0 2px; border: 1px solid #ccc; background: white; cursor: pointer;">5</button>
                </span>
                <button class="page-btn" style="padding: 8px 16px; margin: 0 5px; border: 1px solid #ccc; background: white; cursor: pointer;">下一页</button>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/smart-search.js?v=20250701-5"></script>
    <script src="js/main.js?v=20250701-5"></script>
    <script src="js/products.js?v=20250701-7"></script>



    <script>
        // 智能搜索相关函数已移至 js/products.js 中

        // 重置所有筛选
        function resetAllFilters() {
            const smartSearchInput = document.getElementById('smart-search-input');
            const categoryFilter = document.getElementById('category-filter');
            const carBrandFilter = document.getElementById('car-brand-filter');
            const materialFilter = document.getElementById('material-filter');
            const includeApproximate = document.getElementById('include-approximate');
            const toleranceSelect = document.getElementById('tolerance-select');

            if (smartSearchInput) smartSearchInput.value = '';
            if (categoryFilter) categoryFilter.value = '';
            if (carBrandFilter) carBrandFilter.value = '';
            if (materialFilter) materialFilter.value = '';
            if (includeApproximate) includeApproximate.checked = true;
            if (toleranceSelect) toleranceSelect.value = '3';

            // 重置车型选择器
            loadCarModels();

            // 显示所有产品
            resetFilters();

            // 隐藏搜索历史
            const searchHistory = document.getElementById('search-history');
            if (searchHistory) searchHistory.style.display = 'none';

            // 清除统计信息
            const searchStats = document.getElementById('search-stats');
            if (searchStats) searchStats.textContent = '';
        }

        // 更新搜索统计
        function updateSearchStats(results, query) {
            const statsContainer = document.getElementById('search-stats');
            if (statsContainer) {
                const exactCount = results.exact.length;
                const approximateCount = results.approximate.length;
                const totalCount = exactCount + approximateCount;

                if (query && totalCount > 0) {
                    let statsText = `🎯 搜索"${query}"：找到 ${totalCount} 个结果`;
                    if (exactCount > 0 && approximateCount > 0) {
                        statsText += ` (精确匹配 ${exactCount} 个，相近推荐 ${approximateCount} 个)`;
                    }
                    statsContainer.textContent = statsText;
                    statsContainer.style.display = 'block';
                } else if (query && totalCount === 0) {
                    statsContainer.textContent = `😔 搜索"${query}"：未找到相关结果，请尝试其他关键词`;
                    statsContainer.style.display = 'block';
                    statsContainer.style.background = 'rgba(220, 53, 69, 0.1)';
                    statsContainer.style.color = '#dc3545';
                } else {
                    statsContainer.style.display = 'none';
                    statsContainer.style.background = 'rgba(190, 19, 27, 0.1)';
                    statsContainer.style.color = '#be131b';
                }
            }
        }

        // 显示搜索历史
        function showSearchHistory() {
            const historyContainer = document.getElementById('search-history');
            const historyTags = document.getElementById('history-tags');
            const history = smartSearch.getSearchHistory();

            if (history.length > 0) {
                let html = '';
                history.slice(0, 8).forEach(item => {
                    html += `
                        <span class="history-tag" onclick="useHistorySearch('${item}')">${item}</span>
                    `;
                });
                historyTags.innerHTML = html;
                historyContainer.style.display = 'block';
            }
        }

        // 使用历史搜索
        function useHistorySearch(query) {
            document.getElementById('smart-search-input').value = query;
            performSmartSearch();
        }

        // 加载车型数据（从产品的car_models字段中提取）
        async function loadCarModels() {
            try {
                console.log('开始从产品数据中加载车型信息...');

                // 获取所有产品的车型信息
                const { data: products, error } = await supabase
                    .from('products')
                    .select('car_models')
                    .not('car_models', 'is', null);

                if (error) {
                    console.error('加载产品车型数据失败:', error);
                    loadDefaultCarModels();
                    return;
                }

                console.log('产品车型数据加载成功:', products);

                if (!products || products.length === 0) {
                    console.log('没有找到车型数据，使用默认车型');
                    loadDefaultCarModels();
                    return;
                }

                // 从产品的car_models字段中提取所有车型
                const allCarModels = new Set();
                products.forEach(product => {
                    if (product.car_models) {
                        // 按逗号分割车型，并清理空格
                        const models = product.car_models.split(',').map(model => model.trim());
                        models.forEach(model => {
                            if (model) allCarModels.add(model);
                        });
                    }
                });

                // 提取品牌（取车型名称的第一部分作为品牌）
                const brands = new Set();
                allCarModels.forEach(model => {
                    // 提取品牌名（如"奥迪A4L" -> "奥迪"）
                    const brandMatch = model.match(/^([\u4e00-\u9fa5]+)/);
                    if (brandMatch) {
                        brands.add(brandMatch[1]);
                    }
                });

                console.log('提取到的品牌:', Array.from(brands));
                console.log('提取到的车型:', Array.from(allCarModels));

                // 填充品牌选择器
                const brandSelect = document.getElementById('car-brand-filter');
                if (brandSelect) {
                    brandSelect.innerHTML = '<option value="">所有车型</option>';
                    Array.from(brands).sort().forEach(brand => {
                        brandSelect.innerHTML += `<option value="${brand}">${brand}</option>`;
                    });
                    console.log('品牌选择器已更新');
                }

                // 存储车型数据供其他函数使用
                window.carModelsData = Array.from(allCarModels).sort();

            } catch (error) {
                console.error('加载车型数据出错:', error);
                loadDefaultCarModels();
            }
        }

        // 加载默认车型数据
        function loadDefaultCarModels() {
            console.log('加载默认车型数据');
            const defaultBrands = ['奥迪', '宝马', '奔驰', '大众', '丰田', '本田', '日产', '现代', '起亚', '福特'];

            const brandSelect = document.getElementById('car-brand-filter');
            if (brandSelect) {
                brandSelect.innerHTML = '<option value="">所有车型</option>';
                defaultBrands.forEach(brand => {
                    brandSelect.innerHTML += `<option value="${brand}">${brand}</option>`;
                });
                console.log('默认品牌选择器已更新');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCarModels();
        });
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>
