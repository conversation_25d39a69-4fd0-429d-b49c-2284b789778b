# 🚀 新建Supabase项目迁移指南

## 📋 当前状况
- 原Supabase账号已丢失
- 项目仍在运行：`https://snckktsqwrbfwtjlvcfr.supabase.co`
- 管理员账号：`admin` / `admin123` (硬编码)

## 🎯 迁移步骤

### 第一步：立即备份数据
1. 打开 `supabase-backup-tool.html`
2. 点击"备份所有数据"
3. 下载所有备份文件

### 第二步：注册新的Supabase账号
1. 访问 [supabase.com](https://supabase.com)
2. 注册新账号（建议使用你的常用邮箱）
3. 创建新项目

### 第三步：获取新项目配置
创建项目后，获取以下信息：
```
项目URL: https://YOUR_PROJECT_REF.supabase.co
匿名密钥: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 第四步：创建数据库表结构

在新项目的SQL编辑器中执行以下SQL：

```sql
-- 1. 创建用户表
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    user_type TEXT DEFAULT 'guest' CHECK (user_type IN ('guest', 'premium', 'privileged', 'admin')),
    company_name TEXT,
    phone TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 创建产品表
CREATE TABLE products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    data_id TEXT UNIQUE,
    product_name TEXT NOT NULL,
    product_category TEXT,
    specifications TEXT,
    material TEXT,
    attachment_path TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建客服消息表
CREATE TABLE customer_service_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    user_name TEXT,
    message_content TEXT NOT NULL,
    message_type TEXT DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 创建下载记录表
CREATE TABLE download_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    product_id UUID REFERENCES products(id),
    file_type TEXT,
    downloaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 创建RLS策略（重要！）
-- 启用RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_service_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE download_records ENABLE ROW LEVEL SECURITY;

-- 用户表策略
CREATE POLICY "用户可以查看所有用户" ON users FOR SELECT USING (true);
CREATE POLICY "管理员可以管理用户" ON users FOR ALL USING (
    auth.jwt() ->> 'user_type' = 'admin' OR 
    auth.uid()::text = id::text
);

-- 产品表策略
CREATE POLICY "所有人可以查看产品" ON products FOR SELECT USING (true);
CREATE POLICY "管理员可以管理产品" ON products FOR ALL USING (
    auth.jwt() ->> 'user_type' = 'admin'
);

-- 客服消息策略
CREATE POLICY "用户可以查看自己的消息" ON customer_service_messages FOR SELECT USING (
    auth.uid()::text = user_id::text OR 
    auth.jwt() ->> 'user_type' = 'admin'
);
CREATE POLICY "用户可以发送消息" ON customer_service_messages FOR INSERT WITH CHECK (
    auth.uid()::text = user_id::text
);

-- 下载记录策略
CREATE POLICY "用户可以查看自己的下载记录" ON download_records FOR SELECT USING (
    auth.uid()::text = user_id::text OR 
    auth.jwt() ->> 'user_type' = 'admin'
);
CREATE POLICY "用户可以创建下载记录" ON download_records FOR INSERT WITH CHECK (
    auth.uid()::text = user_id::text
);

-- 6. 创建管理员账户
INSERT INTO users (username, email, user_type, is_active) VALUES 
('admin', '<EMAIL>', 'admin', true);
```

### 第五步：更新项目配置

需要更新以下文件中的Supabase配置：

#### 主要文件列表：
- `admin/login.html`
- `admin/users.html`
- `admin/products.html`
- `admin/test-auth.html`
- `debug-supabase.html`
- `supabase-backup-tool.html`
- 所有包含Supabase配置的HTML文件

#### 配置更新模板：
```javascript
const supabase = window.supabase.createClient(
    'https://YOUR_NEW_PROJECT.supabase.co',  // 替换为新的URL
    'YOUR_NEW_ANON_KEY'                      // 替换为新的匿名密钥
);
```

### 第六步：恢复数据

1. 使用备份的JSON文件
2. 在新项目中导入数据
3. 验证数据完整性

### 第七步：测试功能

1. 测试管理员登录：`admin` / `admin123`
2. 测试用户管理功能
3. 测试产品管理功能
4. 测试客服功能

## 🔧 临时解决方案

在迁移完成之前，你可以：

1. **使用简化版用户管理**：
   - 打开 `admin/users-simple.html`
   - 这个版本尝试绕过RLS限制

2. **继续使用现有功能**：
   - 产品管理可能仍然正常工作
   - 前端用户功能应该不受影响

3. **定期备份**：
   - 每天运行备份工具
   - 保存所有重要数据

## ⚠️ 重要提醒

1. **时间紧迫**：原项目可能随时停止工作
2. **数据安全**：确保所有备份都已下载
3. **测试充分**：新环境部署后要全面测试
4. **用户通知**：如果需要停机迁移，提前通知用户

## 📞 需要帮助？

如果在迁移过程中遇到问题，可以：
1. 查看浏览器控制台的错误信息
2. 使用调试工具测试连接
3. 检查RLS策略配置

---
**创建日期**: 2025-07-16
**状态**: 紧急迁移指南