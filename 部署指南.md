# CentOS 8 云主机部署指南

## 🚀 部署步骤

### 1. 连接到云主机
```bash
ssh root@your_server_ip
```

### 2. 更新系统
```bash
# 更新系统包
dnf update -y

# 安装必要工具
dnf install -y wget curl git unzip
```

### 3. 安装Nginx
```bash
# 安装Nginx
dnf install -y nginx

# 启动并设置开机自启
systemctl start nginx
systemctl enable nginx

# 检查状态
systemctl status nginx
```

### 4. 配置防火墙
```bash
# 开放HTTP和HTTPS端口
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp

# 重载防火墙配置
firewall-cmd --reload

# 查看开放的端口
firewall-cmd --list-all
```

### 5. 部署网站文件
```bash
# 创建网站目录
mkdir -p /var/www/chunsheng

# 进入网站目录
cd /var/www/chunsheng

# 上传网站文件（方法1：使用scp从本地上传）
# 在本地执行：scp -r chunsheng-website/* root@your_server_ip:/var/www/chunsheng/

# 或者方法2：使用git（如果有代码仓库）
# git clone your_repository_url .

# 设置文件权限
chown -R nginx:nginx /var/www/chunsheng
chmod -R 755 /var/www/chunsheng
```

### 6. 配置Nginx虚拟主机
```bash
# 创建Nginx配置文件
cat > /etc/nginx/conf.d/chunsheng.conf << 'EOF'
server {
    listen 80;
    server_name your_domain.com www.your_domain.com;  # 替换为您的域名
    root /var/www/chunsheng;
    index index.html index.htm;

    # 日志文件
    access_log /var/log/nginx/chunsheng_access.log;
    error_log /var/log/nginx/chunsheng_error.log;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 主要位置配置
    location / {
        try_files $uri $uri/ =404;
    }

    # 管理后台访问控制（可选）
    location /admin/ {
        # 可以添加IP白名单
        # allow your_ip;
        # deny all;
        try_files $uri $uri/ =404;
    }

    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 隐藏Nginx版本
    server_tokens off;
}
EOF

# 测试Nginx配置
nginx -t

# 重启Nginx
systemctl restart nginx
```

### 7. 配置SSL证书（推荐）
```bash
# 安装Certbot
dnf install -y python3-certbot-nginx

# 获取SSL证书（替换为您的域名）
certbot --nginx -d your_domain.com -d www.your_domain.com

# 设置自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### 8. 优化配置
```bash
# 创建优化的Nginx主配置
cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup

cat > /etc/nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 20M;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    include /etc/nginx/conf.d/*.conf;
}
EOF

# 重启Nginx
systemctl restart nginx
```

## 📁 文件上传方法

### 方法1：使用SCP上传
```bash
# 在本地执行（Windows可使用WinSCP或PowerShell）
scp -r chunsheng-website/* root@your_server_ip:/var/www/chunsheng/
```

### 方法2：使用SFTP
```bash
# 连接SFTP
sftp root@your_server_ip

# 上传文件
put -r chunsheng-website/* /var/www/chunsheng/
```

### 方法3：使用rsync（推荐）
```bash
# 同步文件（保持权限和时间戳）
rsync -avz --progress chunsheng-website/ root@your_server_ip:/var/www/chunsheng/
```

## 🔧 配置Supabase连接

### 更新Supabase配置
```bash
# 编辑Supabase配置文件
vi /var/www/chunsheng/js/supabase-config.js

# 确保配置正确
# const SUPABASE_URL = 'your_supabase_url'
# const SUPABASE_ANON_KEY = 'your_supabase_anon_key'
```

## 🛡️ 安全配置

### 1. 设置文件权限
```bash
# 设置正确的文件权限
find /var/www/chunsheng -type f -exec chmod 644 {} \;
find /var/www/chunsheng -type d -exec chmod 755 {} \;
chown -R nginx:nginx /var/www/chunsheng
```

### 2. 配置SELinux（如果启用）
```bash
# 检查SELinux状态
sestatus

# 如果SELinux启用，设置正确的上下文
setsebool -P httpd_can_network_connect 1
restorecon -R /var/www/chunsheng
```

## 📊 监控和日志

### 查看访问日志
```bash
tail -f /var/log/nginx/chunsheng_access.log
```

### 查看错误日志
```bash
tail -f /var/log/nginx/chunsheng_error.log
```

### 监控系统资源
```bash
# 安装htop
dnf install -y htop

# 查看系统资源
htop
```

## 🔄 维护命令

### 重启服务
```bash
systemctl restart nginx
```

### 更新网站文件
```bash
# 备份当前文件
cp -r /var/www/chunsheng /var/www/chunsheng_backup_$(date +%Y%m%d)

# 上传新文件后重新设置权限
chown -R nginx:nginx /var/www/chunsheng
chmod -R 755 /var/www/chunsheng
```

### 查看服务状态
```bash
systemctl status nginx
```

## 🌐 域名配置

1. 在域名服务商处添加A记录指向您的服务器IP
2. 等待DNS生效（通常几分钟到几小时）
3. 使用域名访问网站

## ✅ 部署完成检查

1. 访问 `http://your_domain.com` 查看首页
2. 访问 `http://your_domain.com/admin/` 查看管理后台
3. 测试产品页面和客服功能
4. 检查SSL证书是否正常工作

## 🆘 常见问题

### 1. 403 Forbidden错误
```bash
# 检查文件权限
ls -la /var/www/chunsheng
# 重新设置权限
chown -R nginx:nginx /var/www/chunsheng
```

### 2. 502 Bad Gateway错误
```bash
# 检查Nginx配置
nginx -t
# 重启Nginx
systemctl restart nginx
```

### 3. 无法访问网站
```bash
# 检查防火墙
firewall-cmd --list-all
# 检查Nginx状态
systemctl status nginx
```
