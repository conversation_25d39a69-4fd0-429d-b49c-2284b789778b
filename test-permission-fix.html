<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .status {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .user-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>权限系统测试页面</h1>
    
    <div class="test-section">
        <h2>当前状态</h2>
        <div class="status" id="current-status">
            正在检查权限状态...
        </div>
        <button onclick="checkPermissions()">刷新权限状态</button>
    </div>

    <div class="test-section">
        <h2>模拟登录测试</h2>
        <button onclick="simulateLogin('mengbu', 'premium')">模拟 mengbu 登录 (premium)</button>
        <button onclick="simulateLogin('admin', 'admin')">模拟管理员登录</button>
        <button onclick="simulateLogin('privileged', 'privileged')">模拟特殊用户登录</button>
        <button onclick="simulateLogout()">模拟登出</button>
    </div>

    <div class="test-section">
        <h2>权限检查结果</h2>
        <div id="permission-results">
            点击"刷新权限状态"查看结果
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>

    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkPermissions();
            }, 2000);
        });

        function checkPermissions() {
            const statusDiv = document.getElementById('current-status');
            const resultsDiv = document.getElementById('permission-results');
            
            // 检查全局变量
            const globalUserType = window.currentUserType;
            const globalUser = window.currentUser;
            
            // 检查本地存储
            const savedUser = localStorage.getItem('simple_auth_user');
            const loginTime = localStorage.getItem('simple_auth_login_time');
            
            let statusHTML = `
                <h3>全局变量状态：</h3>
                <p><strong>window.currentUserType:</strong> ${globalUserType || '未设置'}</p>
                <p><strong>window.currentUser:</strong> ${globalUser ? globalUser.username : '未设置'}</p>
                
                <h3>本地存储状态：</h3>
                <p><strong>simple_auth_user:</strong> ${savedUser ? '已保存' : '未保存'}</p>
                <p><strong>simple_auth_login_time:</strong> ${loginTime ? new Date(parseInt(loginTime)).toLocaleString() : '未保存'}</p>
            `;
            
            if (savedUser) {
                try {
                    const user = JSON.parse(savedUser);
                    statusHTML += `
                        <div class="user-info">
                            <h4>用户详情：</h4>
                            <p><strong>用户名:</strong> ${user.username}</p>
                            <p><strong>邮箱:</strong> ${user.email}</p>
                            <p><strong>用户类型:</strong> ${user.user_type}</p>
                            <p><strong>公司:</strong> ${user.company_name || '未填写'}</p>
                        </div>
                    `;
                } catch (e) {
                    statusHTML += `<p style="color: red;">解析用户数据失败: ${e.message}</p>`;
                }
            }
            
            statusDiv.innerHTML = statusHTML;
            
            // 权限检查结果
            let resultsHTML = `
                <h3>权限检查结果：</h3>
                <p><strong>当前权限级别:</strong> ${globalUserType || 'guest'}</p>
            `;
            
            // 检查各种权限
            if (typeof window.canViewDetails === 'function') {
                resultsHTML += `<p><strong>可查看详情:</strong> ${window.canViewDetails() ? '是' : '否'}</p>`;
            }
            if (typeof window.canDownloadBasic === 'function') {
                resultsHTML += `<p><strong>可下载基础资料:</strong> ${window.canDownloadBasic() ? '是' : '否'}</p>`;
            }
            if (typeof window.canDownload === 'function') {
                resultsHTML += `<p><strong>可下载PDF:</strong> ${window.canDownload() ? '是' : '否'}</p>`;
            }
            
            resultsDiv.innerHTML = resultsHTML;
        }

        function simulateLogin(username, userType) {
            // 模拟用户数据
            const mockUser = {
                id: 'test-' + username,
                username: username,
                email: username + '@test.com',
                user_type: userType,
                company_name: '测试公司'
            };
            
            // 保存到本地存储
            localStorage.setItem('simple_auth_user', JSON.stringify(mockUser));
            localStorage.setItem('simple_auth_login_time', Date.now().toString());
            
            // 更新全局变量
            window.currentUser = mockUser;
            window.currentUserType = userType;
            
            // 触发更新
            if (typeof window.updateUIForUser === 'function') {
                window.updateUIForUser();
            }
            
            console.log('模拟登录:', username, userType);
            checkPermissions();
        }

        function simulateLogout() {
            // 清除本地存储
            localStorage.removeItem('simple_auth_user');
            localStorage.removeItem('simple_auth_login_time');
            
            // 清除全局变量
            window.currentUser = null;
            window.currentUserType = 'guest';
            
            // 触发更新
            if (typeof window.updateUIForUser === 'function') {
                window.updateUIForUser();
            }
            
            console.log('模拟登出');
            checkPermissions();
        }

        // 监听权限状态变化
        window.addEventListener('authReady', function(event) {
            console.log('收到权限状态变化事件:', event.detail);
            checkPermissions();
        });
    </script>
</body>
</html>
