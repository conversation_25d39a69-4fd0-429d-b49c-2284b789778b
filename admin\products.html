<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品管理 - 管理后台</title>
    
    <!-- 样式文件 -->
    <link href="../css/custom.css" rel="stylesheet" type="text/css">
    
    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- Sortable.js for drag & drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    
    <style>
        body {
            margin: 0;
            background: #f5f5f5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            flex-shrink: 0;
        }
        
        .sidebar-header {
            padding: 20px;
            background: #be131b;
            text-align: center;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            border-bottom: 1px solid #34495e;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .top-bar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }
        
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-box input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 250px;
        }
        
        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .btn-primary {
            background: #be131b;
            color: white;
        }
        
        .btn-primary:hover {
            background: #a01117;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .products-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sort-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .sort-controls label {
            font-size: 14px;
            color: #666;
            margin: 0;
        }

        .sort-controls select {
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .table-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .product-row {
            cursor: move;
            transition: background 0.3s ease;
        }
        
        .product-row:hover {
            background: #f8f9fa;
        }
        
        .product-row.sortable-ghost {
            opacity: 0.4;
        }
        
        .product-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .close {
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #be131b;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .import-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: border-color 0.3s ease;
        }
        
        .import-area.dragover {
            border-color: #be131b;
            background: #f8f9fa;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: #be131b;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>管理后台</h2>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="products.html" class="active">📦 产品管理</a></li>
                <li><a href="users.html">👥 用户管理</a></li>
                <li><a href="customer-service.html">💬 客服管理</a></li>
                <li><a href="#" onclick="logout()" style="color: #ff6b6b;">🚪 退出</a></li>
            </ul>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="top-bar">
                <h1 class="page-title">产品管理</h1>
                <div class="user-info">
                    <span>欢迎，<span id="admin-username">管理员</span></span>
                    <button class="btn btn-danger" onclick="logout()">退出登录</button>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="search-box">
                        <input type="text" id="search-input" placeholder="搜索产品名称、编码...">
                        <button class="btn btn-secondary" onclick="searchProducts()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetSearch()">重置</button>
                    </div>
                    
                    <div class="actions">
                        <button class="btn btn-primary" onclick="showAddModal()">➕ 添加产品</button>
                        <button class="btn btn-success" onclick="showImportModal()">📊 批量导入Excel</button>
                        <button class="btn btn-info" onclick="showBatchUploadModal()">📁 批量上传图片/PDF</button>
                        <button class="btn btn-secondary" onclick="downloadTemplate()">📋 下载Excel模板</button>
                        <button class="btn btn-secondary" onclick="exportProducts()">📤 导出数据</button>
                    </div>
                </div>
                
                <!-- 产品表格 -->
                <div class="products-table">
                    <div class="table-header">
                        <h3>产品列表</h3>
                        <div class="table-controls">
                            <div class="sort-controls">
                                <label>排序方式：</label>
                                <select id="sort-field" onchange="applySorting()">
                                    <option value="created_at">上传时间</option>
                                    <option value="data_id">数据ID</option>
                                    <option value="product_name">产品名称</option>
                                    <option value="product_category">产品类别</option>
                                    <option value="stock_code">存货编码</option>
                                    <option value="updated_at">更新时间</option>
                                </select>
                                <select id="sort-order" onchange="applySorting()">
                                    <option value="desc">降序</option>
                                    <option value="asc">升序</option>
                                </select>
                                <button class="btn btn-sm" onclick="saveSortSettings()">💾 保存设置</button>
                            </div>
                            <div class="table-info">
                                <span id="products-count">0</span> 个产品
                                <button class="btn btn-secondary" onclick="toggleSortMode()" id="sort-toggle">🔄 启用拖拽排序</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>排序</th>
                                    <th>图片</th>
                                    <th>PDF</th>
                                    <th>数据ID</th>
                                    <th>产品名称</th>
                                    <th>类别</th>
                                    <th>规格</th>
                                    <th>材质</th>
                                    <th>适用车型</th>
                                    <th>存货编码</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="products-tbody">
                                <tr>
                                    <td colspan="11" class="loading">
                                        <div class="spinner"></div>
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>

    <!-- 产品编辑模态框 -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">添加产品</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="product-form">
                    <input type="hidden" id="product-id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="data_id">数据ID *</label>
                            <input type="text" id="data_id" name="data_id" required>
                        </div>
                        <div class="form-group">
                            <label for="stock_code">存货编码 *</label>
                            <input type="text" id="stock_code" name="stock_code" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="product_name">产品名称 *</label>
                            <input type="text" id="product_name" name="product_name" required>
                        </div>
                        <div class="form-group">
                            <label for="product_category">产品类别 *</label>
                            <select id="product_category" name="product_category" required>
                                <option value="">请选择</option>
                                <option value="DB">DB</option>
                                <option value="TP">TP</option>
                                <option value="ZC">ZC</option>
                                <option value="ZE">ZE</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="specifications">产品规格 *</label>
                            <input type="text" id="specifications" name="specifications" required>
                        </div>
                        <div class="form-group">
                            <label for="material">材质 *</label>
                            <input type="text" id="material" name="material" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="thickness">料厚</label>
                            <input type="text" id="thickness" name="thickness">
                        </div>
                        <div class="form-group">
                            <label for="shape_code">外形编码</label>
                            <input type="text" id="shape_code" name="shape_code">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">备注</label>
                        <textarea id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="car_models">适用车型</label>

                        <!-- 品牌和车型输入框 -->
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; gap: 15px; margin-bottom: 10px;">
                                <div style="flex: 1;">
                                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #666;">品牌：</label>
                                    <input type="text" id="brand-input" placeholder="输入品牌名称" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" onkeypress="if(event.key==='Enter') addCarModelFromInputs()">
                                </div>
                                <div style="flex: 1;">
                                    <label style="display: block; margin-bottom: 5px; font-size: 12px; color: #666;">车型：</label>
                                    <input type="text" id="model-input" placeholder="输入车型" style="width: 100%; padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" onkeypress="if(event.key==='Enter') addCarModelFromInputs()">
                                </div>
                                <div style="display: flex; align-items: end;">
                                    <button type="button" onclick="addCarModelFromInputs()" style="padding: 6px 12px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">添加到车型</button>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: #999; text-align: center;">
                                输入品牌和车型后点击"添加到车型"按钮，或按回车键快速添加
                            </div>
                        </div>

                        <!-- 车型文本框 -->
                        <textarea id="car_models" name="car_models" rows="2" placeholder="请输入适用车型，多个车型用逗号分隔，例如：奥迪A4L,宝马3系,奔驰C级" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                        <small style="color: #666; font-size: 12px;">可以使用上方输入框快速添加，或直接在此输入，多个车型用逗号分隔</small>
                    </div>

                    <!-- 图片上传区域 -->
                    <div class="form-group">
                        <label for="product_image">产品图片</label>
                        <div class="image-upload-area" id="image-upload-area" style="border: 2px dashed #ddd; border-radius: 8px; padding: 20px; text-align: center; margin-bottom: 10px; transition: border-color 0.3s ease;">
                            <div id="current-image" style="display: none; margin-bottom: 15px;">
                                <img id="current-image-preview" src="" alt="当前图片" style="max-width: 200px; max-height: 150px; border-radius: 4px;">
                                <div style="margin-top: 10px;">
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteCurrentImage()">🗑️ 删除图片</button>
                                </div>
                            </div>
                            <div id="upload-area">
                                <div style="font-size: 48px; color: #ddd; margin-bottom: 10px;">📷</div>
                                <p>点击选择图片或拖拽图片到此处</p>
                                <p style="font-size: 12px; color: #666;">支持 JPG, PNG, GIF 格式，最大 5MB</p>
                                <p style="font-size: 12px; color: #999;">文件将自动复制到 images 目录</p>
                                <input type="file" id="image-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">
                                <button type="button" class="btn btn-secondary" onclick="document.getElementById('image-upload').click()">选择图片</button>
                            </div>
                        </div>
                        <input type="hidden" id="product_image" name="product_image">
                    </div>

                    <!-- PDF附件上传区域 -->
                    <div class="form-group">
                        <label for="attachment_path">技术文档 (PDF)</label>
                        <div class="pdf-upload-area" id="pdf-upload-area" style="border: 2px dashed #ddd; border-radius: 8px; padding: 20px; text-align: center; margin-bottom: 10px; transition: border-color 0.3s ease;">
                            <div id="current-pdf" style="display: none; margin-bottom: 15px;">
                                <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                                    <span style="font-size: 24px;">📄</span>
                                    <span id="current-pdf-name">当前PDF文件</span>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteCurrentPdf()">🗑️ 删除</button>
                                </div>
                            </div>
                            <div id="pdf-upload-area-inner">
                                <div style="font-size: 48px; color: #ddd; margin-bottom: 10px;">📄</div>
                                <p>点击选择PDF文件或拖拽文件到此处</p>
                                <p style="font-size: 12px; color: #666;">支持 PDF 格式，最大 10MB</p>
                                <p style="font-size: 12px; color: #999;">文件将自动上传到云端存储</p>
                                <input type="file" id="pdf-upload" accept=".pdf" style="display: none;" onchange="handlePdfUpload(this)">
                                <button type="button" class="btn btn-secondary" onclick="document.getElementById('pdf-upload').click()">选择PDF</button>
                            </div>
                        </div>
                        <input type="hidden" id="attachment_path" name="attachment_path">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="main_process_1">主要工艺1</label>
                            <input type="text" id="main_process_1" name="main_process_1">
                        </div>
                        <div class="form-group">
                            <label for="main_process_2">主要工艺2</label>
                            <input type="text" id="main_process_2" name="main_process_2">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="main_process_3">主要工艺3</label>
                            <input type="text" id="main_process_3" name="main_process_3">
                        </div>
                        <div class="form-group">
                            <label for="main_process_4">主要工艺4</label>
                            <input type="text" id="main_process_4" name="main_process_4">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="process_count">工序数</label>
                            <input type="number" id="process_count" name="process_count">
                        </div>
                        <div class="form-group">
                            <label for="detail_description">详细描述</label>
                            <textarea id="detail_description" name="detail_description" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="variable_process_1">可变加工艺1</label>
                            <input type="text" id="variable_process_1" name="variable_process_1">
                        </div>
                        <div class="form-group">
                            <label for="variable_process_2">可变加工艺2</label>
                            <input type="text" id="variable_process_2" name="variable_process_2">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="variable_process_3">可变加工艺3</label>
                        <input type="text" id="variable_process_3" name="variable_process_3">
                    </div>

                    <div style="text-align: right; margin-top: 30px;">
                        <button type="button" class="btn btn-secondary" onclick="hideModal('product-modal')">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveProduct()">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 产品详情模态框 -->
    <div id="product-details-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>产品详情</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="product-details"></div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="import-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📊 批量导入产品数据</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="import-steps">
                    <h4>📋 Excel数据导入步骤：</h4>
                    <ol>
                        <li>点击"下载Excel模板"按钮获取标准模板</li>
                        <li>按照模板格式填写产品基础数据（不包含图片和PDF）</li>
                        <li>保存为CSV格式文件</li>
                        <li>上传CSV文件进行批量导入</li>
                        <li>导入完成后，可使用"批量上传图片/PDF"功能添加文件</li>
                    </ol>
                    <div style="background: #e3f2fd; padding: 10px; border-radius: 4px; margin-top: 10px;">
                        <strong>💡 提示：</strong>图片和PDF文件请使用单独的"批量上传图片/PDF"功能，文件名需要与产品的数据ID对应。
                    </div>
                </div>

                <div class="import-area" id="import-area">
                    <div>
                        <h3>📁 选择CSV文件</h3>
                        <p>拖拽文件到此处或点击选择文件</p>
                        <input type="file" id="import-file" class="file-input" accept=".csv" onchange="handleImportFile(this)">
                        <button class="upload-btn" onclick="document.getElementById('import-file').click()">选择文件</button>
                        <button class="upload-btn" onclick="downloadTemplate()" style="background: #6c757d;">下载模板</button>
                    </div>
                </div>

                <div id="import-preview" style="display: none; margin-top: 20px;">
                    <h4>数据预览：</h4>
                    <div id="preview-content" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;"></div>
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-secondary" onclick="cancelImport()">取消</button>
                        <button class="btn btn-primary" onclick="confirmImport()">确认导入</button>
                    </div>
                </div>

                <div id="import-progress" style="display: none; text-align: center; margin-top: 20px;">
                    <div class="spinner"></div>
                    <p>正在导入数据...</p>
                    <div id="progress-info"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量文件上传模态框 -->
    <div id="batch-upload-modal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h2>📁 批量上传图片和PDF文件</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="upload-tabs" style="display: flex; margin-bottom: 20px; border-bottom: 1px solid #ddd;">
                    <button class="tab-btn active" onclick="switchUploadTab('images')" data-tab="images" style="flex: 1; padding: 10px; border: none; background: none; cursor: pointer; border-bottom: 2px solid #be131b;">
                        📷 批量上传图片
                    </button>
                    <button class="tab-btn" onclick="switchUploadTab('pdfs')" data-tab="pdfs" style="flex: 1; padding: 10px; border: none; background: none; cursor: pointer; border-bottom: 2px solid transparent;">
                        📄 批量上传PDF
                    </button>
                </div>

                <!-- 图片上传标签页 -->
                <div id="images-tab" class="upload-tab-content">
                    <div class="upload-instructions" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h4>📷 批量图片上传说明：</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>支持格式：</strong>JPG, PNG, GIF</li>
                            <li><strong>文件大小：</strong>单个文件最大 5MB</li>
                            <li><strong>命名规则：</strong>文件名必须与产品的数据ID对应</li>
                            <li><strong>命名示例：</strong>A0001.jpg, A0002.png, A0003.gif</li>
                            <li><strong>自动匹配：</strong>系统会根据文件名自动关联到对应的产品</li>
                            <li><strong>批量操作：</strong>可以同时选择多个图片文件进行上传</li>
                        </ul>
                        <div style="background: #fff3cd; padding: 8px; border-radius: 4px; margin-top: 10px; font-size: 14px;">
                            <strong>⚠️ 注意：</strong>请确保产品数据已经存在于系统中，否则无法关联图片。
                        </div>
                    </div>

                    <div class="batch-upload-area" id="images-upload-area" style="border: 2px dashed #ddd; border-radius: 8px; padding: 40px; text-align: center; margin-bottom: 20px; transition: border-color 0.3s ease;">
                        <div style="font-size: 48px; color: #ddd; margin-bottom: 15px;">📷</div>
                        <h3>拖拽图片文件到此处或点击选择</h3>
                        <p style="color: #666; margin-bottom: 20px;">可以同时选择多个图片文件</p>
                        <input type="file" id="batch-images-input" multiple accept="image/*" style="display: none;" onchange="handleBatchImageUpload(this)">
                        <button class="btn btn-primary" onclick="document.getElementById('batch-images-input').click()">选择图片文件</button>
                    </div>

                    <div id="images-preview" style="display: none;">
                        <h4>图片预览：</h4>
                        <div id="images-preview-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px;"></div>
                        <div style="text-align: right;">
                            <button class="btn btn-secondary" onclick="clearImagePreview()">清除</button>
                            <button class="btn btn-primary" onclick="uploadBatchImages()">开始上传</button>
                        </div>
                    </div>
                </div>

                <!-- PDF上传标签页 -->
                <div id="pdfs-tab" class="upload-tab-content" style="display: none;">
                    <div class="upload-instructions" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h4>📄 批量PDF上传说明：</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>支持格式：</strong>PDF</li>
                            <li><strong>文件大小：</strong>单个文件最大 10MB</li>
                            <li><strong>命名规则：</strong>文件名必须包含产品的数据ID</li>
                            <li><strong>命名示例：</strong>A0001_技术文档.pdf, A0002_说明书.pdf, A0003.pdf</li>
                            <li><strong>自动匹配：</strong>系统会根据文件名中的数据ID自动关联到对应的产品</li>
                            <li><strong>批量操作：</strong>可以同时选择多个PDF文件进行上传</li>
                        </ul>
                        <div style="background: #fff3cd; padding: 8px; border-radius: 4px; margin-top: 10px; font-size: 14px;">
                            <strong>⚠️ 注意：</strong>请确保产品数据已经存在于系统中，否则无法关联PDF文件。
                        </div>
                    </div>

                    <div class="batch-upload-area" id="pdfs-upload-area" style="border: 2px dashed #ddd; border-radius: 8px; padding: 40px; text-align: center; margin-bottom: 20px; transition: border-color 0.3s ease;">
                        <div style="font-size: 48px; color: #ddd; margin-bottom: 15px;">📄</div>
                        <h3>拖拽PDF文件到此处或点击选择</h3>
                        <p style="color: #666; margin-bottom: 20px;">可以同时选择多个PDF文件</p>
                        <input type="file" id="batch-pdfs-input" multiple accept=".pdf" style="display: none;" onchange="handleBatchPdfUpload(this)">
                        <button class="btn btn-primary" onclick="document.getElementById('batch-pdfs-input').click()">选择PDF文件</button>
                    </div>

                    <div id="pdfs-preview" style="display: none;">
                        <h4>PDF文件列表：</h4>
                        <div id="pdfs-preview-list" style="margin-bottom: 20px;"></div>
                        <div style="text-align: right;">
                            <button class="btn btn-secondary" onclick="clearPdfPreview()">清除</button>
                            <button class="btn btn-primary" onclick="uploadBatchPdfs()">开始上传</button>
                        </div>
                    </div>
                </div>

                <!-- 上传进度 -->
                <div id="upload-progress" style="display: none; margin-top: 20px;">
                    <h4>上传进度：</h4>
                    <div style="background: #f8f9fa; border-radius: 8px; padding: 20px;">
                        <div id="progress-bar-container" style="background: #e9ecef; border-radius: 10px; height: 20px; margin-bottom: 15px; overflow: hidden;">
                            <div id="progress-bar" style="background: linear-gradient(90deg, #be131b, #d32f2f); height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 10px;"></div>
                        </div>
                        <div id="progress-text" style="text-align: center; font-weight: bold;">0%</div>
                        <div id="progress-details" style="margin-top: 10px; font-size: 14px; color: #666;"></div>
                    </div>
                </div>

                <!-- 上传结果 -->
                <div id="upload-results" style="display: none; margin-top: 20px;">
                    <h4>上传结果：</h4>
                    <div id="upload-results-content"></div>
                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-primary" onclick="hideModal('batch-upload-modal'); loadProducts();">完成</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/supabase-config.js"></script>
    <script src="js/admin-common.js"></script>
    <script src="js/file-upload-manager.js"></script>
    <script src="js/products-admin.js"></script>
    <script>


        // 初始化拖拽上传功能（保留以防需要）
        function initializeDragAndDrop() {
            // 图片拖拽
            const imageUploadArea = document.getElementById('image-upload-area');
            if (imageUploadArea) {
                imageUploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#be131b';
                    this.style.backgroundColor = '#f8f9fa';
                });

                imageUploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#ddd';
                    this.style.backgroundColor = 'transparent';
                });

                imageUploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#ddd';
                    this.style.backgroundColor = 'transparent';

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const file = files[0];
                        if (file.type.startsWith('image/')) {
                            document.getElementById('image-upload').files = files;
                            handleImageUpload(document.getElementById('image-upload'));
                        } else {
                            showError('请选择图片文件');
                        }
                    }
                });
            }

            // PDF拖拽
            const pdfUploadArea = document.getElementById('pdf-upload-area');
            if (pdfUploadArea) {
                pdfUploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#be131b';
                    this.style.backgroundColor = '#f8f9fa';
                });

                pdfUploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#ddd';
                    this.style.backgroundColor = 'transparent';
                });

                pdfUploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.style.borderColor = '#ddd';
                    this.style.backgroundColor = 'transparent';

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const file = files[0];
                        if (file.type === 'application/pdf') {
                            document.getElementById('pdf-upload').files = files;
                            handlePdfUpload(document.getElementById('pdf-upload'));
                        } else {
                            showError('请选择PDF文件');
                        }
                    }
                });
            }
        }

        // 图片上传处理函数
        async function handleImageUpload(input) {
            const file = input.files[0];
            if (!file) return;

            // 验证文件类型和大小
            if (!file.type.startsWith('image/')) {
                showError('请选择有效的图片文件');
                return;
            }

            if (file.size > 5 * 1024 * 1024) {
                showError('图片文件不能超过5MB');
                return;
            }

            try {
                // 显示上传进度
                showSuccess('正在上传图片...');

                // 生成文件名
                const dataId = document.getElementById('data_id').value || 'temp';
                const fileExt = file.name.split('.').pop();
                const fileName = `${dataId}_${Date.now()}.${fileExt}`;
                const filePath = `images/${fileName}`;

                // 上传到Supabase Storage
                const { error } = await supabase.storage
                    .from('product-images')
                    .upload(filePath, file, {
                        cacheControl: '3600',
                        upsert: true
                    });

                if (error) throw error;

                // 获取公共URL
                const { data: urlData } = supabase.storage
                    .from('product-images')
                    .getPublicUrl(filePath);

                // 更新表单
                document.getElementById('product_image').value = urlData.publicUrl;

                console.log('图片上传成功，URL:', urlData.publicUrl);
                console.log('表单字段值:', document.getElementById('product_image').value);

                // 显示预览
                showImagePreview(urlData.publicUrl);

                showSuccess('图片上传成功');

            } catch (error) {
                console.error('图片上传失败:', error);
                showError('图片上传失败: ' + error.message);
            }
        }

        // 文件名清理函数 - 移除中文字符和特殊字符
        function sanitizeFileName(fileName) {
            // 中文到英文的映射
            const chineseToEnglish = {
                '技术文档': 'technical_doc',
                '说明书': 'manual',
                '产品手册': 'product_manual',
                '工艺文档': 'process_doc',
                '检验标准': 'inspection_standard',
                '用户指南': 'user_guide',
                '安装说明': 'installation_guide'
            };

            // 替换中文词汇
            let cleanName = fileName;
            for (const [chinese, english] of Object.entries(chineseToEnglish)) {
                cleanName = cleanName.replace(chinese, english);
            }

            // 移除其他中文字符和特殊字符，只保留字母、数字、下划线、连字符和点
            cleanName = cleanName.replace(/[^\w\-_.]/g, '_');

            // 移除多余的下划线
            cleanName = cleanName.replace(/_+/g, '_');

            return cleanName;
        }

        // PDF上传处理函数 - 自动上传到Supabase Storage
        async function handlePdfUpload(input) {
            const file = input.files[0];
            if (!file) return;

            // 验证文件类型和大小
            if (file.type !== 'application/pdf') {
                showError('请选择有效的PDF文件');
                return;
            }

            if (file.size > 10 * 1024 * 1024) {
                showError('PDF文件不能超过10MB');
                return;
            }

            try {
                // 显示上传进度
                showSuccess('正在上传PDF...');

                // 生成文件名：使用原始文件名并清理中文字符
                const dataId = document.getElementById('data_id').value || 'temp';
                const originalName = file.name.replace('.pdf', '');
                const cleanOriginalName = sanitizeFileName(originalName);
                const fileName = `${dataId}_${cleanOriginalName}.pdf`;
                const filePath = `pdfs/${fileName}`;

                // 上传到Supabase Storage
                const { error } = await supabase.storage
                    .from('product-pdfs')
                    .upload(filePath, file, {
                        cacheControl: '3600',
                        upsert: true
                    });

                if (error) throw error;

                // 获取公共URL
                const { data: urlData } = supabase.storage
                    .from('product-pdfs')
                    .getPublicUrl(filePath);

                // 更新表单
                document.getElementById('attachment_path').value = urlData.publicUrl;

                // 显示预览
                showPdfPreview(fileName);

                showSuccess('PDF上传成功');

            } catch (error) {
                console.error('PDF上传失败:', error);
                showError('PDF上传失败: ' + error.message);
            }
        }

        // 显示图片预览
        function showImagePreview(imageUrl) {
            const currentImageDiv = document.getElementById('current-image');
            const uploadArea = document.getElementById('upload-area');
            const preview = document.getElementById('current-image-preview');

            preview.src = imageUrl;
            currentImageDiv.style.display = 'block';
            uploadArea.style.display = 'none';
        }

        // 显示PDF预览
        function showPdfPreview(fileName) {
            const currentPdfDiv = document.getElementById('current-pdf');
            const uploadArea = document.getElementById('pdf-upload-area');
            const pdfName = document.getElementById('current-pdf-name');

            pdfName.textContent = fileName;
            currentPdfDiv.style.display = 'block';
            uploadArea.style.display = 'none';
        }

        // 删除当前图片
        async function deleteCurrentImage() {
            const imageUrl = document.getElementById('product_image').value;

            if (!imageUrl) return;

            if (!confirm('确定要删除当前图片吗？')) return;

            try {
                // 从URL中提取文件路径
                const urlParts = imageUrl.split('/');
                const fileName = urlParts[urlParts.length - 1];
                const filePath = `images/${fileName}`;

                // 从Storage中删除文件
                const { error } = await supabase.storage
                    .from('product-images')
                    .remove([filePath]);

                if (error) {
                    console.warn('删除Storage文件失败:', error);
                }

                // 清除表单值
                document.getElementById('product_image').value = '';

                // 隐藏预览，显示上传区域
                document.getElementById('current-image').style.display = 'none';
                document.getElementById('upload-area').style.display = 'block';

                showSuccess('图片已删除');

            } catch (error) {
                console.error('删除图片失败:', error);
                showError('删除图片失败: ' + error.message);
            }
        }

        // 删除当前PDF
        async function deleteCurrentPdf() {
            const pdfUrl = document.getElementById('attachment_path').value;

            if (!pdfUrl) return;

            if (!confirm('确定要删除当前PDF吗？')) return;

            try {
                // 从URL中提取文件路径
                const urlParts = pdfUrl.split('/');
                const fileName = urlParts[urlParts.length - 1];
                const filePath = `pdfs/${fileName}`;

                // 从Storage中删除文件
                const { error } = await supabase.storage
                    .from('product-pdfs')
                    .remove([filePath]);

                if (error) {
                    console.warn('删除Storage文件失败:', error);
                }

                // 清除表单值
                document.getElementById('attachment_path').value = '';

                // 隐藏预览，显示上传区域
                document.getElementById('current-pdf').style.display = 'none';
                document.getElementById('pdf-upload-area').style.display = 'block';

                showSuccess('PDF已删除');

            } catch (error) {
                console.error('删除PDF失败:', error);
                showError('删除PDF失败: ' + error.message);
            }
        }

        // 从品牌和车型输入框添加到车型字段
        function addCarModelFromInputs() {
            const brandInput = document.getElementById('brand-input');
            const modelInput = document.getElementById('model-input');
            const carModelsTextarea = document.getElementById('car_models');

            const brand = brandInput.value.trim();
            const model = modelInput.value.trim();

            if (!brand && !model) {
                alert('请输入品牌或车型');
                return;
            }

            // 构建车型字符串
            let carModelString = '';
            if (brand && model) {
                carModelString = `${brand}${model}`;
            } else if (brand) {
                carModelString = brand;
            } else {
                carModelString = model;
            }

            // 获取当前车型文本框的内容
            const currentCarModels = carModelsTextarea.value.trim();

            // 检查是否已存在
            if (currentCarModels) {
                const existingModels = currentCarModels.split(',').map(m => m.trim());
                if (existingModels.includes(carModelString)) {
                    alert('该车型已存在');
                    return;
                }
                carModelsTextarea.value = currentCarModels + ',' + carModelString;
            } else {
                carModelsTextarea.value = carModelString;
            }

            // 清空输入框
            brandInput.value = '';
            modelInput.value = '';

            // 提示成功
            showSuccess(`已添加车型: ${carModelString}`);
        }

        // 保存产品函数
        async function saveProduct() {
            console.log('saveProduct函数被调用');

            const form = document.getElementById('product-form');
            const formData = new FormData(form);
            const productId = document.getElementById('product-id').value;

            // 构建产品数据
            const productData = {};
            for (let [key, value] of formData.entries()) {
                if (key !== 'product-id') {
                    productData[key] = value.trim();
                }
            }

            console.log('保存产品数据:', productData);
            console.log('图片URL:', productData.product_image);
            console.log('PDF URL:', productData.attachment_path);

            // 验证必填字段
            const requiredFields = ['data_id', 'stock_code', 'product_name', 'product_category', 'specifications', 'material'];
            const missingFields = requiredFields.filter(field => !productData[field]);

            if (missingFields.length > 0) {
                showError(`请填写必填字段: ${missingFields.join(', ')}`);
                return;
            }

            try {
                let result;

                if (productId) {
                    console.log('更新产品，ID:', productId);
                    // 更新产品
                    result = await supabase
                        .from('products')
                        .update(productData)
                        .eq('id', productId);
                } else {
                    console.log('添加新产品');
                    // 添加产品
                    result = await supabase
                        .from('products')
                        .insert([productData]);
                }

                if (result.error) {
                    console.error('数据库操作错误:', result.error);
                    throw result.error;
                }

                console.log('产品保存成功:', result);
                showSuccess(productId ? '产品更新成功' : '产品添加成功');
                hideModal('product-modal');

                // 重新加载产品列表
                console.log('开始重新加载产品列表...');
                if (typeof loadProducts === 'function') {
                    await loadProducts();
                    console.log('产品列表重新加载完成');
                } else {
                    console.warn('loadProducts函数未定义，手动刷新页面');
                    window.location.reload();
                }

            } catch (error) {
                console.error('保存产品失败:', error);
                showError('保存产品失败: ' + error.message);
            }
        }



        // 页面加载时调用
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('页面加载完成，检查认证状态');
            if (await checkAdminAuth()) {
                console.log('认证通过，开始显示产品');
                setTimeout(loadAndDisplayProducts, 2000); // 延迟2秒确保Supabase已初始化
                initializeDragAndDrop(); // 初始化拖拽功能
            }
        });

        // 创建模态框函数
        function createModals() {
            // 模态框已在HTML中定义
        }

        // 显示批量导入模态框
        function showImportModal() {
            showModal('import-modal');
        }

        // 显示批量上传模态框
        function showBatchUploadModal() {
            showModal('batch-upload-modal');
            switchUploadTab('images'); // 默认显示图片上传
        }

        // 切换上传标签页
        function switchUploadTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
                btn.style.borderBottomColor = 'transparent';
            });

            const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
            activeBtn.classList.add('active');
            activeBtn.style.borderBottomColor = '#be131b';

            // 显示对应的内容
            document.querySelectorAll('.upload-tab-content').forEach(content => {
                content.style.display = 'none';
            });

            document.getElementById(`${tabName}-tab`).style.display = 'block';
        }
    </script>
</body>
</html>
