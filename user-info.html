<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户信息 - 安徽春晟机械有限公司</title>
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
    
    <style>
        .user-info-container {
            max-width: 800px;
            margin: 40px auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .info-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .info-section h3 {
            color: #be131b;
            margin-bottom: 15px;
            border-bottom: 2px solid #be131b;
            padding-bottom: 10px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f5f5f5;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #333;
        }
        .info-value {
            color: #666;
        }
        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }
        .permission-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .permission-yes {
            background: #d4edda;
            color: #155724;
        }
        .permission-no {
            background: #f8d7da;
            color: #721c24;
        }
        .login-prompt {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .login-prompt h2 {
            color: #be131b;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #be131b;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background: #a00e15;
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <a href="index.html">
                        <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                    </a>
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="user-info-container">
            <div id="login-prompt" class="login-prompt" style="display: none;">
                <h2>请先登录</h2>
                <p>您需要登录后才能查看用户信息</p>
                <a href="login.html" class="btn">立即登录</a>
                <a href="register.html" class="btn">注册账户</a>
            </div>

            <div id="user-info-content" style="display: none;">
                <h1 style="text-align: center; color: #be131b; margin-bottom: 40px;">用户信息</h1>

                <div class="info-section">
                    <h3>基本信息</h3>
                    <div class="info-item">
                        <span class="info-label">用户名:</span>
                        <span class="info-value" id="username">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮箱:</span>
                        <span class="info-value" id="email">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">权限级别:</span>
                        <span class="info-value" id="user-type">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">公司:</span>
                        <span class="info-value" id="company">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">电话:</span>
                        <span class="info-value" id="phone">-</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>权限详情</h3>
                    <div class="permission-item">
                        <span>查看产品详情</span>
                        <span class="permission-status" id="perm-view-details">-</span>
                    </div>
                    <div class="permission-item">
                        <span>下载基础资料</span>
                        <span class="permission-status" id="perm-download-basic">-</span>
                    </div>
                    <div class="permission-item">
                        <span>下载所有资料</span>
                        <span class="permission-status" id="perm-download-all">-</span>
                    </div>
                    <div class="permission-item">
                        <span>访问管理后台</span>
                        <span class="permission-status" id="perm-admin">-</span>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button onclick="logout()" class="btn">退出登录</button>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>
    <script>
        // 等待认证系统初始化
        window.addEventListener('authReady', function() {
            updateUserInfo();
        });

        // 更新用户信息显示
        function updateUserInfo() {
            const loginPrompt = document.getElementById('login-prompt');
            const userInfoContent = document.getElementById('user-info-content');

            if (window.auth.isLoggedIn()) {
                const user = window.auth.getCurrentUser();
                
                // 显示用户信息
                loginPrompt.style.display = 'none';
                userInfoContent.style.display = 'block';

                // 填充基本信息
                document.getElementById('username').textContent = user.username || '-';
                document.getElementById('email').textContent = user.email || '-';
                document.getElementById('user-type').textContent = getUserTypeDisplay(user.user_type);
                document.getElementById('company').textContent = user.company_name || '未填写';
                document.getElementById('phone').textContent = user.phone || '未填写';

                // 填充权限信息
                updatePermissionStatus('perm-view-details', window.auth.canViewDetails());
                updatePermissionStatus('perm-download-basic', window.auth.canDownloadBasic());
                updatePermissionStatus('perm-download-all', window.auth.canDownloadAll());
                updatePermissionStatus('perm-admin', window.auth.canAccessAdmin());

            } else {
                // 显示登录提示
                loginPrompt.style.display = 'block';
                userInfoContent.style.display = 'none';
            }

            // 更新头部用户操作区域
            window.auth.updateUI();
        }

        // 更新权限状态显示
        function updatePermissionStatus(elementId, hasPermission) {
            const element = document.getElementById(elementId);
            element.textContent = hasPermission ? '✓ 有权限' : '✗ 无权限';
            element.className = 'permission-status ' + (hasPermission ? 'permission-yes' : 'permission-no');
        }

        // 获取用户类型显示名称
        function getUserTypeDisplay(userType) {
            const typeNames = {
                'guest': '游客',
                'premium': '高级用户',
                'privileged': '特许用户',
                'admin': '管理员'
            };
            return typeNames[userType] || '未知';
        }

        // 退出登录
        async function logout() {
            try {
                await window.auth.signOut();
                updateUserInfo();
                alert('退出登录成功！');
            } catch (error) {
                alert('退出登录失败: ' + error.message);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (window.auth && window.auth.isInitialized) {
                updateUserInfo();
            }
        });
    </script>
</body>
</html>
