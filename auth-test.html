<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #be131b;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #a00e15;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>认证系统测试</h1>
        
        <div class="status" id="auth-status">
            <h3>认证状态</h3>
            <p id="status-text">正在检查...</p>
        </div>

        <div class="form-group">
            <h3>登录测试</h3>
            <form id="login-form">
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" value="test123" required>
                </div>
                <button type="submit">登录</button>
                <button type="button" onclick="logout()">登出</button>
            </form>
        </div>

        <div class="form-group">
            <h3>权限测试</h3>
            <button onclick="testPermissions()">测试权限</button>
            <div id="permission-results"></div>
        </div>

        <div id="message" class="message"></div>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-init.js"></script>
    <script src="js/supabase-auth.js"></script>

    <script>
        // 等待认证系统初始化
        window.addEventListener('authReady', function(event) {
            console.log('认证系统已就绪:', event.detail);
            updateStatus();
        });

        // 更新状态显示
        function updateStatus() {
            const statusText = document.getElementById('status-text');
            
            if (window.auth.isLoggedIn()) {
                const user = window.auth.getCurrentUser();
                statusText.innerHTML = `
                    <strong>已登录</strong><br>
                    用户: ${user.username}<br>
                    邮箱: ${user.email}<br>
                    权限: ${user.user_type}<br>
                    公司: ${user.company_name || '未填写'}
                `;
            } else {
                statusText.innerHTML = '<strong>未登录</strong>';
            }
        }

        // 登录表单处理
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            showMessage('正在登录...', 'info');
            
            try {
                await window.auth.signIn(email, password);
                showMessage('登录成功！', 'success');
                updateStatus();
            } catch (error) {
                showMessage('登录失败: ' + error.message, 'error');
            }
        });

        // 登出
        async function logout() {
            try {
                await window.auth.signOut();
                showMessage('登出成功！', 'success');
                updateStatus();
            } catch (error) {
                showMessage('登出失败: ' + error.message, 'error');
            }
        }

        // 测试权限
        function testPermissions() {
            const results = document.getElementById('permission-results');
            
            const permissions = {
                '查看详情': window.auth.canViewDetails(),
                '下载基础资料': window.auth.canDownloadBasic(),
                '下载所有资料': window.auth.canDownloadAll(),
                '访问管理后台': window.auth.canAccessAdmin(),
                '是否管理员': window.auth.isAdmin()
            };
            
            let html = '<h4>权限检查结果:</h4><ul>';
            for (const [name, hasPermission] of Object.entries(permissions)) {
                html += `<li>${name}: <strong style="color: ${hasPermission ? 'green' : 'red'}">${hasPermission ? '✓' : '✗'}</strong></li>`;
            }
            html += '</ul>';
            
            results.innerHTML = html;
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = message;
            messageDiv.className = 'message ' + type;
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 如果认证系统已经初始化，立即更新状态
            if (window.auth && window.auth.isInitialized) {
                updateStatus();
            }
        });
    </script>
</body>
</html>
