// 权限修复脚本
// 在浏览器控制台中运行此脚本来修复权限问题

function fixPermissions() {
    console.log('🔧 开始修复权限问题...');
    
    // 检查本地存储中的用户信息
    const savedUser = localStorage.getItem('simple_auth_user');
    const loginTime = localStorage.getItem('simple_auth_login_time');
    
    console.log('📋 检查本地存储:');
    console.log('- savedUser:', savedUser ? '存在' : '不存在');
    console.log('- loginTime:', loginTime ? new Date(parseInt(loginTime)).toLocaleString() : '不存在');
    
    if (savedUser && loginTime) {
        try {
            // 检查登录是否过期（24小时）
            const now = Date.now();
            const loginTimestamp = parseInt(loginTime);
            const expireTime = 24 * 60 * 60 * 1000; // 24小时
            
            if (now - loginTimestamp < expireTime) {
                const user = JSON.parse(savedUser);
                console.log('👤 找到有效用户:', user.username, user.user_type);
                
                // 更新全局变量
                window.currentUser = user;
                window.currentUserType = user.user_type || 'premium';
                
                console.log('✅ 权限已修复:');
                console.log('- window.currentUserType:', window.currentUserType);
                console.log('- window.currentUser:', window.currentUser.username);
                
                // 更新UI
                if (typeof window.updatePermissionStatus === 'function') {
                    window.updatePermissionStatus();
                    console.log('🔄 UI已更新');
                } else {
                    console.log('⚠️ updatePermissionStatus 函数不存在');
                }
                
                // 触发权限状态变化事件
                window.dispatchEvent(new CustomEvent('authReady', {
                    detail: { userType: window.currentUserType, user: window.currentUser }
                }));
                
                return {
                    success: true,
                    userType: window.currentUserType,
                    username: window.currentUser.username
                };
            } else {
                console.log('❌ 登录已过期');
                // 清除过期的登录信息
                localStorage.removeItem('simple_auth_user');
                localStorage.removeItem('simple_auth_login_time');
                window.currentUser = null;
                window.currentUserType = 'guest';
                
                return {
                    success: false,
                    message: '登录已过期，请重新登录'
                };
            }
        } catch (e) {
            console.error('❌ 解析用户数据失败:', e);
            return {
                success: false,
                error: e.message
            };
        }
    } else {
        console.log('❌ 没有找到登录信息');
        window.currentUser = null;
        window.currentUserType = 'guest';
        
        return {
            success: false,
            message: '没有找到登录信息，请先登录'
        };
    }
}

// 手动设置用户权限（用于测试）
function setUserPermission(username, userType, email) {
    console.log(`🔧 手动设置用户权限: ${username} (${userType})`);
    
    const mockUser = {
        id: 'manual-' + username,
        username: username,
        email: email || username + '@test.com',
        user_type: userType,
        company_name: '测试公司'
    };
    
    // 保存到本地存储
    localStorage.setItem('simple_auth_user', JSON.stringify(mockUser));
    localStorage.setItem('simple_auth_login_time', Date.now().toString());
    
    // 更新全局变量
    window.currentUser = mockUser;
    window.currentUserType = userType;
    
    console.log('✅ 权限设置完成');
    
    // 更新UI
    if (typeof window.updatePermissionStatus === 'function') {
        window.updatePermissionStatus();
    }
    
    return fixPermissions();
}

// 检查当前权限状态
function checkCurrentPermissions() {
    console.log('📊 当前权限状态:');
    console.log('- window.currentUserType:', window.currentUserType);
    console.log('- window.currentUser:', window.currentUser);
    
    const savedUser = localStorage.getItem('simple_auth_user');
    if (savedUser) {
        try {
            const user = JSON.parse(savedUser);
            console.log('- 本地存储用户:', user.username, user.user_type);
        } catch (e) {
            console.log('- 本地存储解析失败:', e.message);
        }
    } else {
        console.log('- 本地存储: 无用户信息');
    }
    
    return {
        globalUserType: window.currentUserType,
        globalUser: window.currentUser,
        localStorage: savedUser ? '有数据' : '无数据'
    };
}

// 导出函数到全局
window.fixPermissions = fixPermissions;
window.setUserPermission = setUserPermission;
window.checkCurrentPermissions = checkCurrentPermissions;

console.log('🚀 权限修复脚本已加载');
console.log('可用函数:');
console.log('- fixPermissions() - 修复权限问题');
console.log('- setUserPermission(username, userType, email) - 手动设置权限');
console.log('- checkCurrentPermissions() - 检查当前权限状态');
console.log('');
console.log('示例用法:');
console.log('setUserPermission("mengbu", "premium", "<EMAIL>")');
