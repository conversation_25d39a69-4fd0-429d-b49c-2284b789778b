# 春晟机械网站 - Linux 8 快速部署指南

## 🚀 部署方案

### 方案一：自动部署（推荐）

1. **连接到您的Linux服务器**
   ```bash
   # 通过VNC或SSH连接到服务器
   # 确保您有root权限
   ```

2. **上传部署脚本**
   ```bash
   # 在服务器上创建目录
   mkdir -p /tmp/chunsheng-deploy
   cd /tmp/chunsheng-deploy
   
   # 上传 deploy.sh 脚本到服务器
   # 可以使用scp、wget或直接复制粘贴
   ```

3. **运行自动部署脚本**
   ```bash
   chmod +x deploy.sh
   sudo ./deploy.sh
   ```

4. **按提示输入信息**
   - 域名：您的域名或服务器IP
   - SSL证书：建议选择 y
   - 网站目录：默认 /var/www/chunsheng

### 方案二：手动部署

#### 步骤1：更新系统
```bash
sudo dnf update -y
sudo dnf install -y wget curl git unzip vim
```

#### 步骤2：安装Nginx
```bash
sudo dnf install -y nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 步骤3：配置防火墙
```bash
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

#### 步骤4：创建网站目录
```bash
sudo mkdir -p /var/www/chunsheng
sudo chown -R nginx:nginx /var/www/chunsheng
sudo chmod -R 755 /var/www/chunsheng
```

#### 步骤5：上传网站文件
```bash
# 方法1：使用scp从本地上传
scp -r chunsheng-website/* root@your_server_ip:/var/www/chunsheng/

# 方法2：在服务器上直接下载（如果有git仓库）
cd /var/www/chunsheng
# git clone your_repository_url .

# 方法3：手动上传文件到VNC桌面，然后复制
cp -r /path/to/uploaded/files/* /var/www/chunsheng/
```

#### 步骤6：配置Nginx
```bash
sudo tee /etc/nginx/conf.d/chunsheng.conf << 'EOF'
server {
    listen 80;
    server_name your_domain_or_ip;
    root /var/www/chunsheng;
    index index.html index.htm;

    access_log /var/log/nginx/chunsheng_access.log;
    error_log /var/log/nginx/chunsheng_error.log;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location / {
        try_files $uri $uri/ =404;
    }

    # 管理后台
    location /admin/ {
        try_files $uri $uri/ =404;
    }

    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    server_tokens off;
}
EOF

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

#### 步骤7：设置文件权限
```bash
sudo chown -R nginx:nginx /var/www/chunsheng
sudo chmod -R 755 /var/www/chunsheng
sudo find /var/www/chunsheng -type f -exec chmod 644 {} \;
```

## 📁 文件上传方法

### 通过VNC桌面上传
1. 在VNC桌面中打开文件管理器
2. 将本地的 chunsheng-website 文件夹上传到服务器
3. 复制到网站目录：
   ```bash
   sudo cp -r /path/to/uploaded/chunsheng-website/* /var/www/chunsheng/
   sudo chown -R nginx:nginx /var/www/chunsheng
   ```

### 通过SCP上传（如果有SSH访问）
```bash
# 在本地执行
scp -r chunsheng-website/* root@your_server_ip:/var/www/chunsheng/
```

### 通过Git上传（如果有代码仓库）
```bash
cd /var/www/chunsheng
git clone your_repository_url .
```

## 🔧 验证部署

1. **检查Nginx状态**
   ```bash
   sudo systemctl status nginx
   ```

2. **检查网站文件**
   ```bash
   ls -la /var/www/chunsheng/
   ```

3. **访问网站**
   - 打开浏览器访问：http://your_server_ip
   - 管理后台：http://your_server_ip/admin/

## 🛠️ 常用维护命令

```bash
# 重启Nginx
sudo systemctl restart nginx

# 查看访问日志
sudo tail -f /var/log/nginx/chunsheng_access.log

# 查看错误日志
sudo tail -f /var/log/nginx/chunsheng_error.log

# 更新网站文件后重新设置权限
sudo chown -R nginx:nginx /var/www/chunsheng
sudo chmod -R 755 /var/www/chunsheng
```

## 🔍 故障排除

### 1. 无法访问网站
```bash
# 检查防火墙
sudo firewall-cmd --list-all

# 检查Nginx状态
sudo systemctl status nginx

# 检查端口监听
sudo ss -tlnp | grep :80
```

### 2. 403 Forbidden错误
```bash
# 检查文件权限
ls -la /var/www/chunsheng/

# 重新设置权限
sudo chown -R nginx:nginx /var/www/chunsheng
sudo chmod -R 755 /var/www/chunsheng
```

### 3. 502 Bad Gateway错误
```bash
# 检查Nginx配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 📞 需要帮助？

如果遇到问题，请提供：
1. 错误信息截图
2. 服务器日志：`sudo tail -20 /var/log/nginx/error.log`
3. 系统信息：`cat /etc/os-release`
