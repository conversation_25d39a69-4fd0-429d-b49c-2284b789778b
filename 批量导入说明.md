# 产品批量导入说明

## 文件格式要求

### 1. 文件类型
- 支持 `.csv` 格式文件
- 编码格式：UTF-8
- 分隔符：逗号 (,)

### 2. 表头字段（必须完全匹配）
```
数据ID,存货编码,产品名称,产品类别,产品规格,材质,料厚,备注,适用车型,外形编码,主要工艺1,主要工艺2,主要工艺3,主要工艺4,工序数,可变加工艺1,可变加工艺2,可变加工艺3
```

## 字段说明

| 字段名 | 是否必填 | 说明 | 示例 |
|--------|----------|------|------|
| 数据ID | 是 | 产品唯一标识 | A0001, T0001 |
| 存货编码 | 是 | 库存管理编码 | CG-ZJ-001 |
| 产品名称 | 是 | 产品名称 | 减震器支架 |
| 产品类别 | 是 | 产品分类代码 | ZJ, GD, ZE, TP, FC, QT |
| 产品规格 | 是 | 产品规格描述 | Φ50×30×2.0 |
| 材质 | 是 | 材料类型 | SPHC, SPCC, Q235 |
| 料厚 | 否 | 材料厚度 | 2.0, 1.5, 3.0 |
| 备注 | 否 | 产品备注信息 | 适用于前减震器 |
| 适用车型 | 否 | 适用车型列表 | 奥迪A4L,宝马3系 |
| 外形编码 | 否 | 外形标识码 | ZJ001, GD002 |
| 主要工艺1-4 | 否 | 主要加工工艺 | 冲压, 折弯, 焊接 |
| 工序数 | 否 | 工序总数 | 3, 4, 5 |
| 可变加工艺1-3 | 否 | 可变加工工艺 | 钻孔, 攻丝, 去毛刺 |

## 产品类别代码

| 代码 | 说明 |
|------|------|
| ZJ | 支架（座）类 |
| GD | 固定圈（防护套）类 |
| ZE | 支耳（板）类 |
| TP | 弹簧盘类 |
| FC | 防尘盖（顶板）类 |
| QT | 其它类 |
| DB | 防尘盖（顶板）类 |
| ZC | 支架（座）类 |

## 车型字段格式

### 单个车型
```
奥迪A4L
```

### 多个车型（用逗号分隔）
```
奥迪A4L,宝马3系,奔驰C级
```

### 注意事项
- 车型名称之间用英文逗号分隔
- 不要在车型名称前后添加空格
- 支持中文车型名称
- 可以包含品牌和具体车型

## 示例数据

### 完整示例
```csv
数据ID,存货编码,产品名称,产品类别,产品规格,材质,料厚,备注,适用车型,外形编码,主要工艺1,主要工艺2,主要工艺3,主要工艺4,工序数,可变加工艺1,可变加工艺2,可变加工艺3
A0001,CG-ZJ-001,前减震器支架,ZJ,Φ52×35×2.5,SPHC,2.5,前悬挂系统支架,"奥迪A4L,宝马3系",ZJ001,冲压,折弯,焊接,表面处理,4,钻孔,攻丝,去毛刺
```

### 简化示例
```csv
数据ID,存货编码,产品名称,产品类别,产品规格,材质,料厚,备注,适用车型,外形编码,主要工艺1,主要工艺2,主要工艺3,主要工艺4,工序数,可变加工艺1,可变加工艺2,可变加工艺3
T0001,TEST-001,测试产品,ZJ,Φ50×30,SPHC,2.0,测试用,"奥迪A4L",ZJ001,冲压,,,1,,,
```

## 导入步骤

1. **准备数据文件**
   - 按照模板格式准备 CSV 文件
   - 确保数据ID唯一性
   - 检查必填字段完整性

2. **登录管理后台**
   - 访问 `/admin/products.html`
   - 使用管理员账号登录

3. **执行导入**
   - 点击"批量导入"按钮
   - 选择准备好的 CSV 文件
   - 等待导入完成

4. **验证结果**
   - 查看导入成功/失败统计
   - 检查产品列表中的新数据
   - 测试车型查询功能

## 常见问题

### Q: 导入失败怎么办？
A: 检查以下几点：
- 文件格式是否为 UTF-8 编码的 CSV
- 表头字段是否完全匹配
- 必填字段是否都有值
- 数据ID是否重复

### Q: 车型查询不到产品？
A: 检查以下几点：
- 车型字段格式是否正确
- 是否使用了英文逗号分隔
- 车型名称是否准确

### Q: 可以修改已导入的产品吗？
A: 可以，有两种方式：
- 在产品列表中点击"编辑"按钮
- 重新导入相同数据ID的产品（会覆盖原数据）

## 提供的示例文件

1. **批量导入产品模板.csv** - 基础模板文件
2. **简化产品导入模板.csv** - 简化版模板
3. **批量导入示例数据.csv** - 包含25个完整示例产品
4. **测试批量导入.csv** - 5个测试产品，用于功能验证

建议先使用测试文件验证导入功能，确认无误后再导入正式数据。
