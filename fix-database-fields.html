<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复数据库字段</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #be131b;
            text-align: center;
        }
        button {
            background: #be131b;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #a00;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #be131b;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复数据库字段</h1>
        
        <div class="step">
            <h3>步骤1：检查当前字段</h3>
            <button onclick="checkCurrentFields()">检查字段</button>
            <div id="check-result" class="result" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>步骤2：添加缺失字段</h3>
            <button onclick="addMissingFields()">添加字段</button>
            <div id="add-result" class="result" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>步骤3：更新现有数据</h3>
            <button onclick="updateExistingData()">更新数据</button>
            <div id="update-result" class="result" style="display: none;"></div>
        </div>

        <div class="step">
            <h3>步骤4：验证修复</h3>
            <button onclick="verifyFix()">验证修复</button>
            <div id="verify-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>

    <script>
        async function checkCurrentFields() {
            const resultDiv = document.getElementById('check-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在检查当前字段...';

            try {
                const { data, error } = await supabase
                    .from('products')
                    .select('*')
                    .limit(1);

                if (error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `错误: ${error.message}`;
                    return;
                }

                if (!data || data.length === 0) {
                    resultDiv.className = 'result warning';
                    resultDiv.textContent = '数据库中没有产品数据';
                    return;
                }

                const existingFields = Object.keys(data[0]);
                resultDiv.className = 'result success';
                resultDiv.textContent = `当前字段 (${existingFields.length}个):\n${existingFields.join('\n')}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `检查失败: ${error.message}`;
            }
        }

        async function addMissingFields() {
            const resultDiv = document.getElementById('add-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在添加缺失字段...';

            const sqlCommands = [
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS material_thickness TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS notes TEXT;", 
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS shape_code TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS main_process_1 TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS main_process_2 TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS main_process_3 TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS main_process_4 TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS process_count INTEGER;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS variable_process_1 TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS variable_process_2 TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS variable_process_3 TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS product_image TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS user_access_level TEXT DEFAULT 'normal';",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS inventory_code TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS product_specs TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS thickness TEXT;",
                "ALTER TABLE products ADD COLUMN IF NOT EXISTS remarks TEXT;"
            ];

            let results = [];
            let successCount = 0;

            for (let i = 0; i < sqlCommands.length; i++) {
                try {
                    const { error } = await supabase.rpc('exec_sql', { 
                        sql_query: sqlCommands[i] 
                    });

                    if (error) {
                        results.push(`❌ ${sqlCommands[i]}: ${error.message}`);
                    } else {
                        results.push(`✅ ${sqlCommands[i]}: 成功`);
                        successCount++;
                    }
                } catch (error) {
                    // 如果rpc不可用，尝试直接执行
                    results.push(`⚠️ ${sqlCommands[i]}: 需要手动执行`);
                }
            }

            resultDiv.className = successCount > 0 ? 'result success' : 'result warning';
            resultDiv.textContent = `添加字段结果:\n\n${results.join('\n')}\n\n成功: ${successCount}/${sqlCommands.length}`;

            if (successCount === 0) {
                resultDiv.textContent += '\n\n请在Supabase控制台的SQL编辑器中手动执行以下命令:\n\n' + sqlCommands.join('\n');
            }
        }

        async function updateExistingData() {
            const resultDiv = document.getElementById('update-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在更新现有数据...';

            try {
                // 获取所有产品
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*');

                if (error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取产品失败: ${error.message}`;
                    return;
                }

                let updateCount = 0;
                let results = [];

                for (const product of products) {
                    const updates = {};
                    
                    // 字段映射和更新
                    if (product.thickness && !product.material_thickness) {
                        updates.material_thickness = product.thickness;
                    }
                    if (product.remarks && !product.notes) {
                        updates.notes = product.remarks;
                    }
                    if (product.product_specs && !product.specifications) {
                        updates.specifications = product.product_specs;
                    }
                    if (product.inventory_code && !product.stock_code) {
                        updates.stock_code = product.inventory_code;
                    }

                    if (Object.keys(updates).length > 0) {
                        const { error: updateError } = await supabase
                            .from('products')
                            .update(updates)
                            .eq('id', product.id);

                        if (updateError) {
                            results.push(`❌ 产品 ${product.product_name}: ${updateError.message}`);
                        } else {
                            results.push(`✅ 产品 ${product.product_name}: 已更新`);
                            updateCount++;
                        }
                    }
                }

                resultDiv.className = 'result success';
                resultDiv.textContent = `数据更新完成:\n\n更新了 ${updateCount} 个产品\n\n${results.join('\n')}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `更新失败: ${error.message}`;
            }
        }

        async function verifyFix() {
            const resultDiv = document.getElementById('verify-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在验证修复...';

            try {
                const { data, error } = await supabase
                    .from('products')
                    .select('*')
                    .limit(3);

                if (error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `验证失败: ${error.message}`;
                    return;
                }

                const requiredFields = [
                    'material_thickness', 'notes', 'shape_code', 
                    'main_process_1', 'main_process_2', 'main_process_3', 'main_process_4'
                ];

                let result = `✅ 验证成功！获取到 ${data.length} 个产品\n\n`;
                
                if (data.length > 0) {
                    const product = data[0];
                    result += '字段验证结果:\n';
                    
                    requiredFields.forEach(field => {
                        const hasField = product.hasOwnProperty(field);
                        const value = product[field];
                        const status = hasField ? '✅' : '❌';
                        result += `${status} ${field}: ${hasField ? (value || '(空)') : '字段不存在'}\n`;
                    });

                    result += '\n所有字段:\n' + Object.keys(product).join(', ');
                }

                resultDiv.className = 'result success';
                resultDiv.textContent = result;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `验证失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
