-- 创建车型表 (car_models)
-- 用于存储汽车品牌和车型信息，支持产品与车型的关联

CREATE TABLE car_models (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    brand TEXT NOT NULL,                    -- 品牌（如：奥迪、宝马、奔驰）
    brand_name TEXT NOT NULL,               -- 品牌车型名称（如：奥迪 A4L）
    model_name TEXT NOT NULL,               -- 具体车型名称（如：2020款 40 TFSI 进取型）
    first_letter TEXT NOT NULL,             -- 首字母（用于分组显示，如：A、B、C）
    product_id TEXT,                        -- 关联的产品ID（可选）
    year_range TEXT,                        -- 适用年份范围（如：2018-2022）
    engine_type TEXT,                       -- 发动机类型（如：2.0T、1.5L）
    transmission TEXT,                      -- 变速箱类型（如：自动、手动）
    fuel_type TEXT,                         -- 燃料类型（如：汽油、柴油、混动）
    body_type TEXT,                         -- 车身类型（如：轿车、SUV、MPV）
    is_active BOOLEAN DEFAULT true,         -- 是否启用
    sort_order INTEGER DEFAULT 0,          -- 排序顺序
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX idx_car_models_brand ON car_models(brand);
CREATE INDEX idx_car_models_first_letter ON car_models(first_letter);
CREATE INDEX idx_car_models_product_id ON car_models(product_id);
CREATE INDEX idx_car_models_is_active ON car_models(is_active);

-- 插入示例数据
INSERT INTO car_models (brand, brand_name, model_name, first_letter, product_id, year_range, engine_type, transmission, fuel_type, body_type) VALUES
('奥迪', '奥迪 A4L', '2020款 40 TFSI 进取型', 'A', 'A0001', '2019-2022', '2.0T', '自动', '汽油', '轿车'),
('奥迪', '奥迪 A6L', '2021款 45 TFSI quattro 臻选动感型', 'A', 'A0002', '2019-2023', '2.0T', '自动', '汽油', '轿车'),
('奥迪', '奥迪 Q5L', '2020款 40 TFSI 荣享进取型', 'A', 'A0003', '2018-2022', '2.0T', '自动', '汽油', 'SUV'),

('宝马', '宝马 3系', '2021款 325Li M运动套装', 'B', 'A0004', '2019-2023', '2.0T', '自动', '汽油', '轿车'),
('宝马', '宝马 5系', '2021款 530Li 领先型 M运动套装', 'B', 'A0005', '2017-2023', '2.0T', '自动', '汽油', '轿车'),
('宝马', '宝马 X3', '2021款 xDrive25i M运动套装', 'B', 'A0006', '2018-2023', '2.0T', '自动', '汽油', 'SUV'),

('奔驰', '奔驰 C级', '2020款 C 260 L 运动版', 'B', 'A0007', '2018-2022', '1.5T', '自动', '汽油', '轿车'),
('奔驰', '奔驰 E级', '2021款 E 300 L 运动豪华型', 'B', 'A0008', '2016-2023', '2.0T', '自动', '汽油', '轿车'),
('奔驰', '奔驰 GLC', '2020款 GLC 260 L 4MATIC 动感型', 'B', 'A0009', '2019-2023', '1.5T', '自动', '汽油', 'SUV'),

('大众', '大众 帕萨特', '2021款 280TSI 商务版', 'D', 'A0010', '2019-2023', '1.4T', '自动', '汽油', '轿车'),
('大众', '大众 迈腾', '2020款 330TSI DSG领先型', 'D', 'A0011', '2016-2023', '2.0T', '自动', '汽油', '轿车'),
('大众', '大众 途观L', '2021款 330TSI 自动两驱风尚版', 'D', 'A0012', '2017-2023', '2.0T', '自动', '汽油', 'SUV'),

('丰田', '丰田 凯美瑞', '2021款 2.0G 豪华版', 'F', 'A0013', '2017-2023', '2.0L', '自动', '汽油', '轿车'),
('丰田', '丰田 雷凌', '2021款 185T CVT运动版', 'F', 'A0014', '2019-2023', '1.2T', '自动', '汽油', '轿车'),
('丰田', '丰田 汉兰达', '2021款 2.0T 四驱精英版 7座', 'F', 'A0015', '2018-2023', '2.0T', '自动', '汽油', 'SUV'),

('本田', '本田 雅阁', '2021款 260TURBO 精英版', 'B', 'A0016', '2018-2023', '1.5T', '自动', '汽油', '轿车'),
('本田', '本田 思域', '2021款 220TURBO CVT劲擎控', 'B', 'A0017', '2019-2023', '1.5T', '自动', '汽油', '轿车'),
('本田', '本田 CR-V', '2021款 240TURBO CVT两驱舒适版', 'B', 'A0018', '2017-2023', '1.5T', '自动', '汽油', 'SUV'),

('日产', '日产 天籁', '2021款 2.0T XV AD1 智享版', 'R', 'A0019', '2019-2023', '2.0T', '自动', '汽油', '轿车'),
('日产', '日产 轩逸', '2021款 1.6L CVT悦享版', 'R', 'A0020', '2019-2023', '1.6L', '自动', '汽油', '轿车'),
('日产', '日产 奇骏', '2021款 2.5L CVT智联领先版 2WD', 'R', 'A0021', '2014-2023', '2.5L', '自动', '汽油', 'SUV'),

('现代', '现代 索纳塔', '2021款 270TGDi GLS', 'X', 'A0022', '2020-2023', '1.5T', '自动', '汽油', '轿车'),
('现代', '现代 伊兰特', '2021款 1.5L CVT智享版', 'X', 'A0023', '2020-2023', '1.5L', '自动', '汽油', '轿车'),
('现代', '现代 途胜L', '2021款 1.5T 两驱精英版', 'X', 'A0024', '2021-2023', '1.5T', '自动', '汽油', 'SUV'),

('起亚', '起亚 K5', '2021款 270T GT-Line', 'Q', 'A0025', '2020-2023', '1.5T', '自动', '汽油', '轿车'),
('起亚', '起亚 K3', '2021款 1.5L CVT智享版', 'Q', 'A0026', '2019-2023', '1.5L', '自动', '汽油', '轿车'),
('起亚', '起亚 智跑', '2021款 1.4T CVT两驱畅享版', 'Q', 'A0027', '2018-2023', '1.4T', '自动', '汽油', 'SUV'),

('福特', '福特 蒙迪欧', '2020款 EcoBoost 245 至尊型', 'F', 'A0028', '2013-2022', '2.0T', '自动', '汽油', '轿车'),
('福特', '福特 福克斯', '2021款 EcoBoost 125 自动锋潮型', 'F', 'A0029', '2018-2023', '1.0T', '自动', '汽油', '轿车'),
('福特', '福特 锐际', '2021款 EcoBoost 245 四驱纵享款ST-LINE', 'F', 'A0030', '2019-2023', '2.0T', '自动', '汽油', 'SUV');

-- 启用RLS（行级安全）
ALTER TABLE car_models ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "所有人可以查看车型" ON car_models FOR SELECT USING (true);
CREATE POLICY "管理员可以管理车型" ON car_models FOR ALL USING (
    auth.jwt() ->> 'user_type' = 'admin'
);

-- 添加注释
COMMENT ON TABLE car_models IS '汽车品牌和车型信息表';
COMMENT ON COLUMN car_models.brand IS '汽车品牌';
COMMENT ON COLUMN car_models.brand_name IS '品牌车型名称';
COMMENT ON COLUMN car_models.model_name IS '具体车型名称';
COMMENT ON COLUMN car_models.first_letter IS '首字母（用于分组）';
COMMENT ON COLUMN car_models.product_id IS '关联的产品ID';
COMMENT ON COLUMN car_models.year_range IS '适用年份范围';
COMMENT ON COLUMN car_models.engine_type IS '发动机类型';
COMMENT ON COLUMN car_models.transmission IS '变速箱类型';
COMMENT ON COLUMN car_models.fuel_type IS '燃料类型';
COMMENT ON COLUMN car_models.body_type IS '车身类型';
