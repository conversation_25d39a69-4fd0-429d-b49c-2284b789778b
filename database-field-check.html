<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库字段检查工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #be131b;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #be131b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #a00;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .warning { border-color: #ffc107; background: #fff3cd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 数据库字段检查工具</h1>
        
        <div class="section">
            <h3>1. 检查Products表字段</h3>
            <p>检查products表中是否包含所有必要的字段</p>
            <button onclick="checkProductsFields()">检查字段</button>
            <div id="fields-result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>2. 检查示例数据</h3>
            <p>查看示例数据中的字段结构</p>
            <button onclick="checkSampleData()">检查示例数据</button>
            <div id="sample-result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>3. 添加缺失字段</h3>
            <p>为products表添加缺失的字段（如果需要）</p>
            <button onclick="addMissingFields()">添加缺失字段</button>
            <div id="add-fields-result" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>4. 测试查询</h3>
            <p>测试所有查询字段是否可用</p>
            <button onclick="testQueries()">测试查询</button>
            <div id="query-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>

    <script>
        // 必需的字段列表
        const requiredFields = [
            'id',
            'data_id',
            'stock_code',
            'inventory_code', // 备用字段
            'product_name',
            'product_category',
            'specifications',
            'product_specs', // 备用字段
            'material',
            'material_thickness',
            'thickness', // 备用字段
            'notes',
            'remarks', // 备用字段
            'shape_code',
            'main_process_1',
            'main_process_2',
            'main_process_3',
            'main_process_4',
            'process_count',
            'variable_process_1',
            'variable_process_2',
            'variable_process_3',
            'attachment_path',
            'product_image',
            'user_access_level',
            'created_at',
            'updated_at'
        ];

        async function checkProductsFields() {
            const resultDiv = document.getElementById('fields-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在检查字段...';

            try {
                // 获取一个产品来检查字段
                const { data, error } = await supabase
                    .from('products')
                    .select('*')
                    .limit(1);

                if (error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `错误: ${error.message}`;
                    return;
                }

                if (!data || data.length === 0) {
                    resultDiv.className = 'result warning';
                    resultDiv.textContent = '数据库中没有产品数据，无法检查字段结构';
                    return;
                }

                const existingFields = Object.keys(data[0]);
                const missingFields = requiredFields.filter(field => !existingFields.includes(field));

                let result = `现有字段 (${existingFields.length}个):\n${existingFields.join(', ')}\n\n`;
                
                if (missingFields.length > 0) {
                    result += `缺失字段 (${missingFields.length}个):\n${missingFields.join(', ')}\n\n`;
                    resultDiv.className = 'result warning';
                } else {
                    result += '✅ 所有必需字段都存在！\n\n';
                    resultDiv.className = 'result success';
                }

                resultDiv.textContent = result;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `检查失败: ${error.message}`;
            }
        }

        async function checkSampleData() {
            const resultDiv = document.getElementById('sample-result');
            resultDiv.style.display = 'block';
            
            // 从supabase-config.js获取示例数据
            if (typeof sampleProducts !== 'undefined') {
                const sampleFields = Object.keys(sampleProducts[0]);
                resultDiv.className = 'result success';
                resultDiv.textContent = `示例数据字段 (${sampleFields.length}个):\n${sampleFields.join(', ')}\n\n示例产品数量: ${sampleProducts.length}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = '无法访问示例数据';
            }
        }

        async function addMissingFields() {
            const resultDiv = document.getElementById('add-fields-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result warning';
            resultDiv.textContent = `注意：添加字段需要数据库管理员权限。
            
建议的SQL命令：

-- 添加缺失字段到products表
ALTER TABLE products ADD COLUMN IF NOT EXISTS inventory_code TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS product_specs TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS thickness TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS remarks TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS material_thickness TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS shape_code TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS main_process_1 TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS main_process_2 TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS main_process_3 TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS main_process_4 TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS process_count INTEGER;
ALTER TABLE products ADD COLUMN IF NOT EXISTS variable_process_1 TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS variable_process_2 TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS variable_process_3 TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS product_image TEXT;
ALTER TABLE products ADD COLUMN IF NOT EXISTS user_access_level TEXT DEFAULT 'normal';

请在Supabase控制台的SQL编辑器中执行这些命令。`;
        }

        async function testQueries() {
            const resultDiv = document.getElementById('query-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在测试查询...';

            try {
                const { data, error } = await supabase
                    .from('products')
                    .select('*')
                    .limit(5);

                if (error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `查询失败: ${error.message}`;
                    return;
                }

                let result = `✅ 查询成功！获取到 ${data.length} 个产品\n\n`;
                
                if (data.length > 0) {
                    result += '第一个产品的字段值:\n';
                    const product = data[0];
                    requiredFields.forEach(field => {
                        const value = product[field];
                        const status = value !== undefined ? '✅' : '❌';
                        result += `${status} ${field}: ${value || '(空)'}\n`;
                    });
                }

                resultDiv.className = 'result success';
                resultDiv.textContent = result;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
