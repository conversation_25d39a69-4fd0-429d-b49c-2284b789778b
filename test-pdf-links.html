<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF链接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>PDF链接测试</h1>
    
    <div class="test-item">
        <h3>测试1: 支耳板PDF</h3>
        <div class="url">https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0006_manual.pdf</div>
        <button class="test-button" onclick="testPDF('https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0006_manual.pdf')">在新窗口打开</button>
        <button class="test-button" onclick="downloadPDF('https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0006_manual.pdf', '支耳板说明书.pdf')">下载</button>
    </div>

    <div class="test-item">
        <h3>测试2: 弹簧盘PDF</h3>
        <div class="url">https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0007_product_manual.pdf</div>
        <button class="test-button" onclick="testPDF('https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0007_product_manual.pdf')">在新窗口打开</button>
        <button class="test-button" onclick="downloadPDF('https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0007_product_manual.pdf', '弹簧盘产品手册.pdf')">下载</button>
    </div>

    <div class="test-item">
        <h3>测试3: 顶板HTML</h3>
        <div class="url">https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0001_技术文档.html</div>
        <button class="test-button" onclick="testPDF('https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0001_技术文档.html')">在新窗口打开</button>
    </div>

    <div class="test-item">
        <h3>测试4: 弹簧盘PDF（相对路径）</h3>
        <div class="url">https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0002_弹簧盘技术文档.pdf</div>
        <button class="test-button" onclick="testPDF('https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0002_弹簧盘技术文档.pdf')">在新窗口打开</button>
        <button class="test-button" onclick="downloadPDF('https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0002_弹簧盘技术文档.pdf', '弹簧盘技术文档.pdf')">下载</button>
    </div>

    <div id="log" style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
        <h3>测试日志:</h3>
        <div id="logContent"></div>
    </div>

    <script>
        function log(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        }

        function testPDF(url) {
            log(`尝试在新窗口打开: ${url}`);
            try {
                const newWindow = window.open(url, '_blank');
                if (newWindow) {
                    log('✅ 新窗口已打开');
                } else {
                    log('❌ 新窗口被阻止');
                }
            } catch (error) {
                log(`❌ 错误: ${error.message}`);
            }
        }

        function downloadPDF(url, filename) {
            log(`尝试下载: ${url} -> ${filename}`);
            try {
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                log('✅ 下载链接已触发');
            } catch (error) {
                log(`❌ 下载错误: ${error.message}`);
            }
        }

        // 页面加载时的测试
        window.addEventListener('load', function() {
            log('页面加载完成，开始测试...');
            
            // 测试第一个PDF链接的可访问性
            const testUrl = 'https://snckktsqwrbfwtjlvcfr.supabase.co/storage/v1/object/public/product-pdfs/pdfs/A0006_manual.pdf';
            
            fetch(testUrl, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        log(`✅ PDF文件可访问: ${testUrl} (状态: ${response.status})`);
                    } else {
                        log(`❌ PDF文件不可访问: ${testUrl} (状态: ${response.status})`);
                    }
                })
                .catch(error => {
                    log(`❌ 网络错误: ${error.message}`);
                });
        });
    </script>
</body>
</html>
