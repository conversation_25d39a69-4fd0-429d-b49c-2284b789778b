<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车型查询功能测试</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #be131b;
            margin-top: 0;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #be131b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #a00e16;
        }
        input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .car-models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .car-model-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>车型查询功能测试</h1>
        
        <!-- 测试1：检查产品中的车型数据 -->
        <div class="test-section">
            <h3>测试1：检查产品中的车型数据</h3>
            <button onclick="testProductCarModels()">检查产品车型数据</button>
            <div id="product-car-models-result" class="result"></div>
        </div>

        <!-- 测试2：提取所有车型和品牌 -->
        <div class="test-section">
            <h3>测试2：提取所有车型和品牌</h3>
            <button onclick="testExtractCarModels()">提取车型和品牌</button>
            <div id="extract-result" class="result"></div>
            <div id="car-models-display" class="car-models-grid"></div>
        </div>

        <!-- 测试3：车型查询功能 -->
        <div class="test-section">
            <h3>测试3：车型查询功能</h3>
            <input type="text" id="search-input" placeholder="输入车型关键词，如：奥迪、A4L、宝马等">
            <button onclick="testCarModelSearch()">搜索产品</button>
            <div id="search-result" class="result"></div>
        </div>

        <!-- 测试4：更新产品车型数据 -->
        <div class="test-section">
            <h3>测试4：更新产品车型数据（仅测试）</h3>
            <p>为一些产品添加车型数据示例：</p>
            <button onclick="updateSampleCarModels()">添加示例车型数据</button>
            <div id="update-result" class="result"></div>
        </div>
    </div>

    <script src="js/supabase-config.js"></script>
    <script>
        // 测试1：检查产品中的车型数据
        async function testProductCarModels() {
            const resultDiv = document.getElementById('product-car-models-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在检查产品车型数据...';

            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('id, data_id, product_name, car_models')
                    .limit(10);

                if (error) throw error;

                let result = `找到 ${products.length} 个产品:\n\n`;
                products.forEach(product => {
                    result += `ID: ${product.id}\n`;
                    result += `数据ID: ${product.data_id || '无'}\n`;
                    result += `产品名称: ${product.product_name}\n`;
                    result += `适用车型: ${product.car_models || '未设置'}\n`;
                    result += '---\n';
                });

                resultDiv.className = 'result success';
                resultDiv.textContent = result;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 测试2：提取所有车型和品牌
        async function testExtractCarModels() {
            const resultDiv = document.getElementById('extract-result');
            const displayDiv = document.getElementById('car-models-display');
            
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在提取车型和品牌信息...';

            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('car_models')
                    .not('car_models', 'is', null);

                if (error) throw error;

                const allCarModels = new Set();
                const brands = new Set();

                products.forEach(product => {
                    if (product.car_models) {
                        const models = product.car_models.split(',').map(model => model.trim());
                        models.forEach(model => {
                            if (model) {
                                allCarModels.add(model);
                                // 提取品牌名
                                const brandMatch = model.match(/^([\u4e00-\u9fa5]+)/);
                                if (brandMatch) {
                                    brands.add(brandMatch[1]);
                                }
                            }
                        });
                    }
                });

                let result = `提取结果:\n`;
                result += `总车型数: ${allCarModels.size}\n`;
                result += `品牌数: ${brands.size}\n\n`;
                result += `品牌列表: ${Array.from(brands).sort().join(', ')}\n\n`;
                result += `车型列表:\n${Array.from(allCarModels).sort().join('\n')}`;

                resultDiv.className = 'result success';
                resultDiv.textContent = result;

                // 显示车型网格
                displayDiv.innerHTML = '';
                Array.from(allCarModels).sort().forEach(model => {
                    const div = document.createElement('div');
                    div.className = 'car-model-item';
                    div.textContent = model;
                    displayDiv.appendChild(div);
                });

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 测试3：车型查询功能
        async function testCarModelSearch() {
            const searchInput = document.getElementById('search-input');
            const resultDiv = document.getElementById('search-result');
            const keyword = searchInput.value.trim();

            if (!keyword) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请输入搜索关键词';
                return;
            }

            resultDiv.className = 'result info';
            resultDiv.textContent = `正在搜索包含 "${keyword}" 的产品...`;

            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('id, data_id, product_name, car_models')
                    .or(`car_models.ilike.%${keyword}%,product_name.ilike.%${keyword}%`);

                if (error) throw error;

                let result = `搜索关键词: "${keyword}"\n`;
                result += `找到 ${products.length} 个相关产品:\n\n`;

                products.forEach(product => {
                    result += `${product.product_name}\n`;
                    result += `  数据ID: ${product.data_id || '无'}\n`;
                    result += `  适用车型: ${product.car_models || '未设置'}\n`;
                    result += '---\n';
                });

                resultDiv.className = 'result success';
                resultDiv.textContent = result;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `搜索错误: ${error.message}`;
            }
        }

        // 测试4：更新产品车型数据
        async function updateSampleCarModels() {
            const resultDiv = document.getElementById('update-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在添加示例车型数据...';

            try {
                // 获取一些没有车型数据的产品
                const { data: products, error } = await supabase
                    .from('products')
                    .select('id, data_id, product_name')
                    .is('car_models', null)
                    .limit(5);

                if (error) throw error;

                if (products.length === 0) {
                    resultDiv.className = 'result info';
                    resultDiv.textContent = '所有产品都已有车型数据';
                    return;
                }

                const sampleCarModels = [
                    '奥迪A4L,宝马3系',
                    '奔驰C级,奥迪A6L',
                    '大众帕萨特,丰田凯美瑞',
                    '本田雅阁,日产天籁',
                    '现代索纳塔,起亚K5'
                ];

                let result = '更新结果:\n';
                for (let i = 0; i < products.length; i++) {
                    const product = products[i];
                    const carModels = sampleCarModels[i % sampleCarModels.length];

                    const { error: updateError } = await supabase
                        .from('products')
                        .update({ car_models: carModels })
                        .eq('id', product.id);

                    if (updateError) {
                        result += `❌ ${product.product_name}: ${updateError.message}\n`;
                    } else {
                        result += `✅ ${product.product_name}: 已设置车型 "${carModels}"\n`;
                    }
                }

                resultDiv.className = 'result success';
                resultDiv.textContent = result;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `更新错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
